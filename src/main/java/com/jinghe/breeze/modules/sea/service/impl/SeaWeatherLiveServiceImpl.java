package com.jinghe.breeze.modules.sea.service.impl;

import com.jinghe.breeze.modules.sea.entity.SeaWeatherLive;
import com.jinghe.breeze.modules.sea.mapper.SeaWeatherLiveMapper;
import com.jinghe.breeze.modules.sea.service.ISeaWeatherLiveService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: sea_weather_live
 * @Author: jeecg-boot
 * @Date:   2024-04-28
 * @Version: V1.0
 */
@Service
public class SeaWeatherLiveServiceImpl extends ServiceImpl<SeaWeatherLiveMapper, SeaWeatherLive> implements ISeaWeatherLiveService {

}
