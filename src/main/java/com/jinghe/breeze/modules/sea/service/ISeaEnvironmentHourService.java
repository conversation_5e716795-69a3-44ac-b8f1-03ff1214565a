package com.jinghe.breeze.modules.sea.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghe.breeze.modules.sea.entity.SeaEnvironmentHour;
import com.jinghe.breeze.modules.sea.entity.SeaWeatherLive;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 海洋环境逐小时
 * @Author: jeecg-boot
 * @Date:   2024-04-25
 * @Version: V1.0
 */
public interface ISeaEnvironmentHourService extends IService<SeaEnvironmentHour> {

    IPage<SeaWeatherLive> pageByTime(Date start, Date end, int pageNumber, int pageSize);

    Map dailyWeather7day(int day);

    List<JSONObject> processAssess();

    List<JSONObject> sailingAssess();

    /**
     * APP出航评估接口
     * @return
     */
    List<JSONObject> sailingAssess2();

    /**
     * APP施工评估接口
     * @return
     */
    List<JSONObject> processAssess2();
}
