package com.jinghe.breeze.modules.sea.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.*;
/**
 * @Description: 极端天气
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Data
@TableName("extreme_weather")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="extreme_weather对象", description="极端天气")
public class ExtremeWeather implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "标识不能为空!")

    @ApiModelProperty(value = "标识")
    private String id;
	/**创建人*/

    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/

    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**删除标识*/
	@Excel(name = "删除标识", width = 15)

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;
	/**组织机构编码*/

    @ApiModelProperty(value = "组织机构编码")
    private String sysOrgCode;
	/**项目id*/
	@Excel(name = "项目id", width = 15)

    @ApiModelProperty(value = "项目id")
    private String projectId;
	/**预警状态*/
	@Excel(name = "预警状态", width = 15)

    @ApiModelProperty(value = "预警状态")
    private String alertStatus;
	/**预警标题*/
	@Excel(name = "预警标题", width = 15)

    @ApiModelProperty(value = "预警标题")
    private String title;
	/**预警级别*/
	@Excel(name = "预警级别", width = 15)

    @ApiModelProperty(value = "预警级别")
    private String warningLevel;
	/**预警类型*/
	@Excel(name = "预警类型", width = 15)

    @ApiModelProperty(value = "预警类型")
    private String type;
	/**发布时间*/
	@Excel(name = "发布时间", width = 20, format = "yyyy-MM-dd HH:mm")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "发布时间")
    private Date releaseDate;
	/**内容*/
	@Excel(name = "内容", width = 15)

    @ApiModelProperty(value = "内容")
    private String content;
	/**预警编号*/
	@Excel(name = "预警编号", width = 15)

    @ApiModelProperty(value = "预警编号")
    private String alertNo;
	/**区域*/
	@Excel(name = "区域", width = 15)

    @ApiModelProperty(value = "区域")
    private String area;
	/**市*/
	@Excel(name = "市", width = 15)

    @ApiModelProperty(value = "市")
    private String city;
	/**预警位置*/
	@Excel(name = "预警位置", width = 15)

    @ApiModelProperty(value = "预警位置")
    private String location;
	/**省*/
	@Excel(name = "省", width = 15)

    @ApiModelProperty(value = "省")
    private String province;
	/**发布来源*/
	@Excel(name = "发布来源", width = 15)

    @ApiModelProperty(value = "发布来源")
    private String releaseSource;
}
