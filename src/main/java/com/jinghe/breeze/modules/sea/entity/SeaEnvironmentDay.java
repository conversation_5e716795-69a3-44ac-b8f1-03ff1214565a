package com.jinghe.breeze.modules.sea.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("sea_environment_day")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sea_environment_day对象", description="海洋环境预报，天")
public class SeaEnvironmentDay implements Serializable {
    private static final long serialVersionUID = 1L;

    /**标识*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;

    //小时标记 2024042201表示2024年4月22日凌晨1点
    private Integer day;
    //海温
    private BigDecimal sstMin;
    private BigDecimal sstMax;

    //水流速度
    private BigDecimal waterSpeedMin;
    private BigDecimal waterSpeedMax;



    //浪高
    private BigDecimal waveHeightMin;
    private BigDecimal waveHeightMax;


    //风速
    private BigDecimal windSpeedMin;
    private BigDecimal windSpeedMax;

}
