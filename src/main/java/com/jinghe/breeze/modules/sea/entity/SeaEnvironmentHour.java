package com.jinghe.breeze.modules.sea.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("sea_environment_hour")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sea_environment_hour对象", description="海洋环境信息，逐小时")
public class SeaEnvironmentHour implements Serializable {
    private static final long serialVersionUID = 1L;

    /**标识*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;

    //小时标记 2024042201表示2024年4月22日凌晨1点
    private Integer hour;
    //海温
    private BigDecimal sst;

    //水流速度
    private BigDecimal waterSpeed;

    //水流方向
    private BigDecimal waterDirection;

    //浪高
    private BigDecimal waveHeight;

    //浪向
    private BigDecimal waveDirection;

    //浪周期
    private BigDecimal wavePeriod;

    //风速
    private BigDecimal windSpeed;

    //风向
    private BigDecimal windDirection;

}
