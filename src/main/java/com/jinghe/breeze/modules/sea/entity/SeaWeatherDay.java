package com.jinghe.breeze.modules.sea.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("sea_weather_day")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sea_weather_day对象", description="海洋天气预报-天")
public class SeaWeatherDay implements Serializable {
    private static final long serialVersionUID = 1L;

    /**标识*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;

    private Integer day;

    private String cityName;//城市名
    private BigDecimal visibility;//能见度 km
    private String weatherDescription;//天气现象
    private Integer weatherCode;//天气代码 参考【海洋天气对照表】菜单
    private Integer clouds;//云层量 %
    private BigDecimal pressure;//气压 mb
    private BigDecimal seaPressure;//海平面气压
    private BigDecimal probability;//降雨概率%
    private BigDecimal precipitation;//降雨量 mm/hr
    private BigDecimal snow;//降雪量 mm/hr
    private BigDecimal snowDepth;//降雪厚度
    private BigDecimal minTemperature;//最低温度 摄氏度℃
    private BigDecimal maxTemperature;//最高温度 摄氏度℃
    private BigDecimal lowTemperature;//夜间最低温度 摄氏度℃
    private BigDecimal highTemperature;//白天最高温度 摄氏度℃
    private BigDecimal averageTemperature;//平局温度 摄氏度℃
    private BigDecimal minAppTemp;//最低体感温度 摄氏度℃
    private BigDecimal maxAppTemp;//最高体感温度 摄氏度℃
    private BigDecimal ultravioletRays;//紫外线指数 范围0-11
    private BigDecimal relaHumidity;//相对湿度 %
    private BigDecimal windDirection;//风向角度度数
    private BigDecimal windSpeed;//风速 m/s
    private String windMark;//风向描述
}
