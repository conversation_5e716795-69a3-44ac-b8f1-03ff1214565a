package com.jinghe.breeze.modules.sea.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghe.breeze.common.constants.enums.WeatherConditionEnum;
import com.jinghe.breeze.common.constants.enums.WeatherRuleEnum;
import com.jinghe.breeze.modules.project.entity.ProjectProcess;
import com.jinghe.breeze.modules.project.mapper.ProjectProcessMapper;
import com.jinghe.breeze.modules.sea.entity.*;
import com.jinghe.breeze.modules.sea.mapper.*;
import com.jinghe.breeze.modules.sea.service.ISeaEnvironmentHourService;
import com.jinghe.breeze.modules.ship.entity.ShipInfo;
import com.jinghe.breeze.modules.ship.entity.ShipOutgoing;
import com.jinghe.breeze.modules.ship.mapper.ShipInfoMapper;
import com.jinghe.breeze.modules.ship.service.IShipOutgoingService;
import org.jeecg.modules.system.entity.SysCategory;
import org.jeecg.modules.system.mapper.SysCategoryMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 海洋环境逐小时
 * @Author: jeecg-boot
 * @Date: 2024-04-25
 * @Version: V1.0
 */
@Service
public class SeaEnvironmentHourServiceImpl extends ServiceImpl<SeaEnvironmentHourMapper, SeaEnvironmentHour> implements ISeaEnvironmentHourService {

    @Autowired
    private SeaEnvironmentHourMapper seaEnHourMapper;
    @Autowired
    private SeaEnvironmentDayMapper seaEnDayMapper;
    @Autowired
    private SeaWeatherLiveMapper seaWeaLiveMapper;
    @Autowired
    private SeaWeatherHourMapper seaWeaHourMapper;
    @Autowired
    private SeaWeatherDayMapper seaWeaDayMapper;

    @Autowired
    private ProjectProcessMapper processMapper;
    @Autowired
    private ShipInfoMapper shipMapper;
    @Autowired
    private SysCategoryMapper sysCategoryMapper;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH");

    @Override
    public IPage<SeaWeatherLive> pageByTime(Date start, Date end, int pageNumber, int pageSize) {
        LambdaQueryWrapper<SeaWeatherLive> liveWeatherWrapper = new LambdaQueryWrapper<>();
        liveWeatherWrapper.orderByDesc(SeaWeatherLive::getDateTime);
        if (start != null) {
            liveWeatherWrapper.gt(SeaWeatherLive::getDateTime, start);
        }
        if (end != null) {
            liveWeatherWrapper.lt(SeaWeatherLive::getDateTime, end);
        }
        return seaWeaLiveMapper.selectPage(new Page<>(pageNumber, pageSize), liveWeatherWrapper);
    }


    @Override
    public Map dailyWeather7day(int day) {
        LambdaQueryWrapper<SeaWeatherLive> liveWeatherWrapper = new LambdaQueryWrapper<>();
        liveWeatherWrapper.orderByDesc(SeaWeatherLive::getDateTime);
        liveWeatherWrapper.last("limit 1");
        SeaWeatherLive seaWeatherLive = seaWeaLiveMapper.selectOne(liveWeatherWrapper);
        seaWeatherLive.setLat("46.524673");
        seaWeatherLive.setLon("83.628303");
        seaWeatherLive.setCityName("额敏县");

        String nowHour = sdf.format(new Date());
        Integer hour = Integer.parseInt(nowHour);
        if (day == 0) {
            day = hour / 100;
        }
        List<SeaWeatherVo> hoursList = new ArrayList<>(200);
        List<SeaWeatherVo> todayHoursList = new ArrayList<>(200);
        LambdaQueryWrapper<SeaWeatherHour> hourWeatherWrapper = new LambdaQueryWrapper<>();
        hourWeatherWrapper.ge(SeaWeatherHour::getHour, hour / 100 * 100);//2024102501,小时数据这样处理后，变为2024102500，可查出当天全天数据
        hourWeatherWrapper.orderByAsc(SeaWeatherHour::getHour);
        hourWeatherWrapper.last("limit 168");
        List<SeaWeatherHour> seaWeatherHours = seaWeaHourMapper.selectList(hourWeatherWrapper);

        LambdaQueryWrapper<SeaEnvironmentHour> hourEnvWrapper = new LambdaQueryWrapper<>();
        hourEnvWrapper.ge(SeaEnvironmentHour::getHour, hour / 100 * 100);
        hourEnvWrapper.orderByAsc(SeaEnvironmentHour::getHour);
        hourEnvWrapper.last("limit 168");
        List<SeaEnvironmentHour> seaEnvHours = seaEnHourMapper.selectList(hourEnvWrapper);
        Map<Integer, SeaEnvironmentHour> collect = seaEnvHours.stream().collect(Collectors.toMap(SeaEnvironmentHour::getHour, v -> v, (oldValue, newValue) -> newValue));
        int i = 0;
        for (SeaWeatherHour weatherHour : seaWeatherHours) {
            SeaWeatherVo vo = new SeaWeatherVo();
            try {
                Date parse = sdf.parse(weatherHour.getHour().toString());
                vo.setTimestamp(parse.getTime());
            } catch (ParseException e) {
                vo.setTimestamp(0L);
            }
            BeanUtils.copyProperties(weatherHour, vo);
            SeaEnvironmentHour seaEnvironmentHour = collect.get(weatherHour.getHour());
            if(seaEnvironmentHour!=null){
                vo.setWaveHeight(seaEnvironmentHour.getWaveHeight());
            }

            hoursList.add(vo);
            if ((weatherHour.getHour() / 100) == day) {
                todayHoursList.add(vo);
            }
            i++;
        }

//        LambdaQueryWrapper<SeaEnvironmentHour> hourEnvironmentWrapper = new LambdaQueryWrapper<>();
//        hourEnvironmentWrapper.ge(SeaEnvironmentHour::getHour, nowHour);
//        hourEnvironmentWrapper.orderByAsc(SeaEnvironmentHour::getHour);
//        hourEnvironmentWrapper.last("limit 168");
//        List<SeaEnvironmentHour> seaEnvironmentHours = seaEnHourMapper.selectList(hourEnvironmentWrapper);
//        Map<Integer, SeaEnvironmentHour> seaEnvironmentHoursMap = seaEnvironmentHours.stream().collect(Collectors.toMap(SeaEnvironmentHour::getHour, vl -> vl, (oldValue, newValue) -> newValue));
//        for (SeaWeatherVo vo : hoursList) {
//            SeaEnvironmentHour seaEnvironmentHour = seaEnvironmentHoursMap.get(vo.getHour());
//            if (seaEnvironmentHour != null) {
//                vo.setWaveHeight(seaEnvironmentHour.getWaveHeight());
//            }
//        }
        LambdaQueryWrapper<SeaWeatherDay> dayWeatherWrapper = new LambdaQueryWrapper<>();
        dayWeatherWrapper.ge(SeaWeatherDay::getDay, hour / 100);
        dayWeatherWrapper.orderByAsc(SeaWeatherDay::getDay);
        dayWeatherWrapper.last("limit 7");
        List<SeaWeatherDay> seaWeatherDays = seaWeaDayMapper.selectList(dayWeatherWrapper);
        LambdaQueryWrapper<SeaEnvironmentDay> dayEnvWrapper = new LambdaQueryWrapper<>();
        dayEnvWrapper.ge(SeaEnvironmentDay::getDay, hour / 100);
        dayEnvWrapper.orderByAsc(SeaEnvironmentDay::getDay);
        dayEnvWrapper.last("limit 7");
        List<SeaEnvironmentDay> seaEnvDays = seaEnDayMapper.selectList(dayEnvWrapper);
        Map<Integer, SeaEnvironmentDay> collect2 = seaEnvDays.stream().collect(Collectors.toMap(SeaEnvironmentDay::getDay, v -> v, (oldValue, newValue) -> newValue));
        List<SeaWeatherVo> weekList = new ArrayList<>(200);
        for (SeaWeatherDay weatherDay : seaWeatherDays) {
            SeaWeatherVo vo = new SeaWeatherVo();
            BeanUtils.copyProperties(weatherDay, vo);
            SeaEnvironmentDay seaEnvironmentDay = collect2.get(weatherDay.getDay());
            if(seaEnvironmentDay!=null){
                vo.setWaveHeight(seaEnvironmentDay.getWaveHeightMax());
            }
            weekList.add(vo);
        }

        Map result = new HashMap();
//        result.put("daily", dayList);
        result.put("today", todayHoursList);
        result.put("hours", hoursList);
        result.put("now", seaWeatherLive);
        result.put("week", weekList);
        return result;
    }

    @Override
    public List<JSONObject> processAssess() {
        List<ProjectProcess> projectProcesses = processMapper.selectList(Wrappers.emptyWrapper());

        List<List<SeaWeatherVo>> childrenList = futureWeather168();

        List<JSONObject> result = new ArrayList<>();
        List<SysCategory> sysCategories = sysCategoryMapper.selectList(new LambdaQueryWrapper<>());
        Map<String, SysCategory> categoryMap = sysCategories.stream()
                .collect(Collectors.toMap(SysCategory -> SysCategory.getCode(), Function.identity()));
        for (int i = 0; i < childrenList.size() && i < 7; i++) {
            List<SeaWeatherVo> children = childrenList.get(i);
            if (!children.isEmpty()) {
                JSONObject dayResult = checkProcess(children, projectProcesses, categoryMap);
                result.add(dayResult);
            }
        }
        return result;
    }

    @Autowired
    private IShipOutgoingService shipOutgoingService;

    @Override
    public List<JSONObject> sailingAssess() {
        LambdaQueryWrapper<ShipOutgoing> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.groupBy(ShipOutgoing::getShipDataId).orderByDesc(ShipOutgoing::getCreateTime);
        List<ShipOutgoing> shipDataIdWithMaxCreateTime = shipOutgoingService.getBaseMapper().selectList(queryWrapper1);
        LambdaQueryWrapper<ShipInfo> shipWrapper = new LambdaQueryWrapper<>();
        shipWrapper.eq(ShipInfo::getIsAlarm, 1);
//        shipWrapper.eq(ShipInfo::getStatus, 1);
        List<ShipInfo> shipInfos = shipMapper.selectList(shipWrapper);
        List<List<SeaWeatherVo>> childrenList = futureWeather168();
        Iterator<ShipInfo> iterator = shipInfos.iterator();
        while (iterator.hasNext()) {
            ShipInfo next = iterator.next();
            Optional<ShipOutgoing> first = shipDataIdWithMaxCreateTime.stream().filter(x -> x.getShipDataId().equals(next.getId())).findFirst();
            if (first.isPresent() && first.get().getIsOut().equals("0")) {
                next.setStatus("0");
            } else {
                iterator.remove();
            }
        }
        List<JSONObject> result = new ArrayList<>();
        for (int i = 0; i < childrenList.size() && i < 7; i++) {
            List<SeaWeatherVo> children = childrenList.get(i);
            JSONObject dayResult = checkSailing(children, shipInfos);
            result.add(dayResult);
        }
        return result;
    }

    private List<List<SeaWeatherVo>> futureWeather168() {
        String nowHour = sdf.format(new Date());
        Integer hour = Integer.parseInt(nowHour);
        LambdaQueryWrapper<SeaWeatherHour> hourWeatherWrapper = new LambdaQueryWrapper<>();
        hourWeatherWrapper.ge(SeaWeatherHour::getHour, hour);
        hourWeatherWrapper.orderByAsc(SeaWeatherHour::getHour);
        hourWeatherWrapper.last("limit 168");
        List<SeaWeatherHour> seaWeatherHours = seaWeaHourMapper.selectList(hourWeatherWrapper);

        LambdaQueryWrapper<SeaEnvironmentHour> hourEnvironmentWrapper = new LambdaQueryWrapper<>();
        hourEnvironmentWrapper.ge(SeaEnvironmentHour::getHour, hour);
        hourEnvironmentWrapper.orderByAsc(SeaEnvironmentHour::getHour);
        hourEnvironmentWrapper.last("limit 168");
        List<SeaEnvironmentHour> seaEnvironmentHours = seaEnHourMapper.selectList(hourEnvironmentWrapper);
        Map<Integer, SeaEnvironmentHour> seaEnvironmentHoursMap = seaEnvironmentHours.stream().collect(Collectors.toMap(SeaEnvironmentHour::getHour, vl -> vl, (oldValue, newValue) -> newValue));

        List<List<SeaWeatherVo>> childrenList = new ArrayList<>();
        int tempHour = 0;
        List<SeaWeatherVo> hourList = new ArrayList<>();
        for (int i = 0; i < seaWeatherHours.size(); i++) {
            SeaWeatherHour seaWeatherHour = seaWeatherHours.get(i);
            if (i == 0) {
                tempHour = seaWeatherHour.getHour();
            }
            Integer itemHour = seaWeatherHour.getHour();
            if (itemHour - tempHour > 2) {
                childrenList.add(hourList);
                hourList = new ArrayList<>();
            }
            tempHour = itemHour;
            SeaWeatherVo vo = new SeaWeatherVo();
            BeanUtils.copyProperties(seaWeatherHour, vo);

            SeaEnvironmentHour seaEnvironmentHour = seaEnvironmentHoursMap.get(itemHour);
            if (seaEnvironmentHour != null) {
                BeanUtils.copyProperties(seaEnvironmentHour, vo);
                vo.setWaveHeight(seaEnvironmentHour.getWaveHeight());

                hourList.add(vo);
            }
            try {
                vo.setWindSpeed(seaWeatherHour.getWindSpeed());
            } catch (Exception e) {
                log.error("风速数据不规范");
            }
        }
        childrenList.add(hourList);
        return childrenList;
    }

    private List<List<SeaWeatherVo>> futureWeather7day() {
        String nowHour = sdf.format(new Date());
        Integer hour = Integer.parseInt(nowHour);
        Integer start = hour / 100 * 100;
        LambdaQueryWrapper<SeaWeatherHour> hourWeatherWrapper = new LambdaQueryWrapper<>();
        hourWeatherWrapper.ge(SeaWeatherHour::getHour, start);
        hourWeatherWrapper.orderByAsc(SeaWeatherHour::getHour);
        hourWeatherWrapper.last("limit 168");
        List<SeaWeatherHour> seaWeatherHours = seaWeaHourMapper.selectList(hourWeatherWrapper);

        LambdaQueryWrapper<SeaEnvironmentHour> hourEnvironmentWrapper = new LambdaQueryWrapper<>();
        hourEnvironmentWrapper.ge(SeaEnvironmentHour::getHour, start);
        hourEnvironmentWrapper.orderByAsc(SeaEnvironmentHour::getHour);
        hourEnvironmentWrapper.last("limit 168");
        List<SeaEnvironmentHour> seaEnvironmentHours = seaEnHourMapper.selectList(hourEnvironmentWrapper);
        Map<Integer, SeaEnvironmentHour> seaEnvironmentHoursMap = seaEnvironmentHours.stream().collect(Collectors.toMap(SeaEnvironmentHour::getHour, vl -> vl, (oldValue, newValue) -> newValue));

        List<List<SeaWeatherVo>> childrenList = new ArrayList<>();
        int tempHour = 0;
        List<SeaWeatherVo> hourList = new ArrayList<>();
        for (int i = 0; i < seaWeatherHours.size(); i++) {
            SeaWeatherHour seaWeatherHour = seaWeatherHours.get(i);
            if (i == 0) {
                tempHour = seaWeatherHour.getHour();
            }
            Integer itemHour = seaWeatherHour.getHour();
            if (itemHour - tempHour > 2) {
                childrenList.add(hourList);
                hourList = new ArrayList<>();
            }
            tempHour = itemHour;
            SeaWeatherVo vo = new SeaWeatherVo();
            BeanUtils.copyProperties(seaWeatherHour, vo);

            SeaEnvironmentHour seaEnvironmentHour = seaEnvironmentHoursMap.get(itemHour);
            if (seaEnvironmentHour != null) {
                BeanUtils.copyProperties(seaEnvironmentHour, vo);
                vo.setWaveHeight(seaEnvironmentHour.getWaveHeight());
                hourList.add(vo);
            }
            try {
                vo.setWindSpeed(seaWeatherHour.getWindSpeed());
            } catch (Exception e) {
                log.error("风速数据不规范");
            }
        }
        childrenList.add(hourList);
        return childrenList;
    }

    @Override
    public List<JSONObject> sailingAssess2() {
        LambdaQueryWrapper<ShipOutgoing> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.groupBy(ShipOutgoing::getShipDataId).orderByDesc(ShipOutgoing::getCreateTime);
        List<ShipOutgoing> shipDataIdWithMaxCreateTime = shipOutgoingService.getBaseMapper().selectList(queryWrapper1);
        LambdaQueryWrapper<ShipInfo> shipWrapper = new LambdaQueryWrapper<>();
        shipWrapper.eq(ShipInfo::getIsAlarm, 1);
//        shipWrapper.eq(ShipInfo::getStatus, 1);
        List<ShipInfo> shipInfos = shipMapper.selectList(shipWrapper);
        List<List<SeaWeatherVo>> childrenList = futureWeather7day();
        Iterator<ShipInfo> iterator = shipInfos.iterator();
        while (iterator.hasNext()) {
            ShipInfo next = iterator.next();
            Optional<ShipOutgoing> first = shipDataIdWithMaxCreateTime.stream().filter(x -> x.getShipDataId().equals(next.getId())).findFirst();
            if (first.isPresent() && first.get().getIsOut().equals("0")) {
                next.setStatus("0");
            } else {
                iterator.remove();
            }
        }
        List<JSONObject> result = new ArrayList<>();
        for (int i = 0; i < childrenList.size() && i < 7; i++) {
            List<SeaWeatherVo> children = childrenList.get(i);
            JSONObject dayResult = checkSailing2(children, shipInfos);
            result.add(dayResult);
        }
        return result;
    }

    @Override
    public List<JSONObject> processAssess2() {
        List<ProjectProcess> projectProcesses = processMapper.selectList(Wrappers.emptyWrapper());

        List<List<SeaWeatherVo>> childrenList = futureWeather7day();

        List<JSONObject> result = new ArrayList<>();
        for (int i = 0; i < childrenList.size() && i < 7; i++) {
            List<SeaWeatherVo> children = childrenList.get(i);
            JSONObject dayResult = checkProcess2(children, projectProcesses);
            result.add(dayResult);
        }
        return result;
    }

    /**
     * 对比工序和天气，是否适合施工
     *
     * @param hoursWeather 当天逐小时天气
     * @param processes    所有工序
     */
    private JSONObject checkProcess(List<SeaWeatherVo> hoursWeather, List<ProjectProcess> processes, Map<String, SysCategory> categoryMap) {
        //当天预警气象类型
        Map<String, String> alarms = new HashMap();
        //当天预警气象阈值（20.1）
        Map<String, Double> alarmCondition = new HashMap();
        //当天预警气象判断方式（>,<)
        Map<String, String> alarmRule = new HashMap();
        //当天预警气象影响时长（1，2，3，4）
        Map<String, Set<Integer>> alarmDuration = new HashMap();
        List<JSONObject> processeCheckResult = new ArrayList<>();
        for (ProjectProcess process : processes) {
            JSONObject processCheckInfo = new JSONObject();
            processCheckInfo.put("type", process.getType());

            processCheckInfo.put("typeName", ObjectUtils.isEmpty(categoryMap.get(process.getType())) ? "" : categoryMap.get(process.getType()).getName());
            processCheckInfo.put("name", process.getName());
            Integer warningEnable = process.getWarningEnable();
            if (warningEnable == 0) {
                continue;
            }
            String information = process.getInformation();
            processCheckInfo.put("rule", information);
            processRules(information, hoursWeather, alarms, alarmCondition, alarmRule, alarmDuration, processCheckInfo);
            processeCheckResult.add(processCheckInfo);
        }
//        String dateTitle = formatDateTitle(hoursWeather.get(0).getHour());

        JSONObject dayResult = new JSONObject();
        try {
            Date parse = sdf.parse(hoursWeather.get(0).getHour().toString());
            dayResult.put("title", parse.getTime());
        } catch (ParseException e) {
            dayResult.put("title", System.currentTimeMillis());
        }

        List<JSONObject> dayAlarm = new ArrayList<>();
        for (Map.Entry<String, Set<Integer>> entry : alarmDuration.entrySet()) {
            String key = entry.getKey();
            Set<Integer> value = entry.getValue();
            JSONObject json = new JSONObject();
            json.put("name", key);
            json.put("symbol", alarmRule.get(key));
            json.put("num", alarmCondition.get(key));
            json.put("hours", value.size());
            dayAlarm.add(json);
        }
        dayResult.put("alarms", dayAlarm);
        dayResult.put("processCheck", processeCheckResult);
        return dayResult;
    }

    /**
     * 对比是否适合出航
     *
     * @param hoursWeather 未来168小时天气
     * @param ships        待对比的船舶信息
     * @return
     */
    private JSONObject checkSailing(List<SeaWeatherVo> hoursWeather, List<ShipInfo> ships) {
        //当天预警气象类型
        Map<String, String> alarms = new HashMap();
        //当天预警气象阈值（20.1）
        Map<String, Double> alarmCondition = new HashMap();
        //当天预警气象判断方式（>,<)
        Map<String, String> alarmRule = new HashMap();
        //当天预警气象影响时长（1，2，3，4）
        Map<String, Set<Integer>> alarmDuration = new HashMap();
        List<JSONObject> processeCheckResult = new ArrayList<>();
        for (ShipInfo ship : ships) {
            Integer isAlarm = ship.getIsAlarm();
            if (isAlarm == 0) {
                continue;
            }
            JSONObject processCheckInfo = new JSONObject();
            processCheckInfo.put("type", ship.getTypeValue());
            processCheckInfo.put("name", ship.getName());
            String information = ship.getInformation();
            processCheckInfo.put("rule", information);
            processRules(information, hoursWeather, alarms, alarmCondition, alarmRule, alarmDuration, processCheckInfo);
            processeCheckResult.add(processCheckInfo);
        }
        JSONObject dayResult = new JSONObject();
        try {
            Date parse = sdf.parse(hoursWeather.get(0).getHour().toString());
            dayResult.put("title", parse.getTime());
        } catch (ParseException e) {
            dayResult.put("title", System.currentTimeMillis());
        }
        List<JSONObject> dayAlarm = new ArrayList<>();
        for (Map.Entry<String, Set<Integer>> entry : alarmDuration.entrySet()) {
            String key = entry.getKey();
            Set<Integer> value = entry.getValue();
            JSONObject json = new JSONObject();
            json.put("name", key);
            json.put("symbol", alarmRule.get(key));
            json.put("num", alarmCondition.get(key));
            json.put("hours", value.size());
            dayAlarm.add(json);
        }
        dayResult.put("alarms", dayAlarm);
        dayResult.put("processCheck", processeCheckResult);
        return dayResult;
    }

    /**
     * 对比是否适合施工（APP）
     *
     * @param hoursWeather 未来168小时天气
     * @param processes    工序
     * @return
     */
    private JSONObject checkProcess2(List<SeaWeatherVo> hoursWeather, List<ProjectProcess> processes) {
        //当天各气象指标最大值
        Map<String, Double> todayMax = new HashMap();
        //当天各气象指标最小值
        Map<String, Double> todayMin = new HashMap();
        //当天各气象指标超出最大值
        Map<String, Integer> todayOverMax = new HashMap();
        //当天各气象指标超出最小值
        Map<String, Integer> todayOverMin = new HashMap();
        List<JSONObject> processeCheckResult = new ArrayList<>();
        for (SeaWeatherVo weather : hoursWeather) {
            JSONObject processCheckInfo = new JSONObject();
            try {
                Date parse = sdf.parse(weather.getHour().toString());
                processCheckInfo.put("timestamp", parse.getTime());
            } catch (ParseException e) {
                processCheckInfo.put("timestamp", System.currentTimeMillis());
            }
            processRules3(weather, processes, todayMax, todayMin, todayOverMax, todayOverMin, processCheckInfo);
            processeCheckResult.add(processCheckInfo);
        }
        JSONObject dayResult = new JSONObject();
        try {
            Date parse = sdf.parse(hoursWeather.get(0).getHour().toString());
            dayResult.put("timestamp", parse.getTime());
        } catch (ParseException e) {
            dayResult.put("timestamp", System.currentTimeMillis());
        }
        dayResult.put("max", todayMax);
        dayResult.put("min", todayMin);
        dayResult.put("maxOver", todayOverMax);
        dayResult.put("minOver", todayOverMin);
        dayResult.put("processCheck", processeCheckResult);
        return dayResult;
    }


    /**
     * 对比是否适合出航（APP）
     *
     * @param hoursWeather 未来168小时天气
     * @param ships        待对比的船舶信息
     * @return
     */
    private JSONObject checkSailing2(List<SeaWeatherVo> hoursWeather, List<ShipInfo> ships) {
        //当天各气象指标最大值
        Map<String, Double> todayMax = new HashMap();
        //当天各气象指标最小值
        Map<String, Double> todayMin = new HashMap();
        //当天各气象指标超出最大值
        Map<String, Integer> todayOverMax = new HashMap();
        //当天各气象指标超出最小值
        Map<String, Integer> todayOverMin = new HashMap();
        List<JSONObject> processeCheckResult = new ArrayList<>();
        for (SeaWeatherVo weather : hoursWeather) {
            JSONObject processCheckInfo = new JSONObject();
            try {
                Date parse = sdf.parse(weather.getHour().toString());
                processCheckInfo.put("timestamp", parse.getTime());
            } catch (ParseException e) {
                processCheckInfo.put("timestamp", System.currentTimeMillis());
            }
            processRules2(weather, ships, todayMax, todayMin, todayOverMax, todayOverMin, processCheckInfo);
            processeCheckResult.add(processCheckInfo);
        }
        JSONObject dayResult = new JSONObject();
        try {
            Date parse = sdf.parse(hoursWeather.get(0).getHour().toString());
            dayResult.put("timestamp", parse.getTime());
        } catch (ParseException e) {
            dayResult.put("timestamp", System.currentTimeMillis());
        }
        dayResult.put("max", todayMax);
        dayResult.put("min", todayMin);
        dayResult.put("maxOver", todayOverMax);
        dayResult.put("minOver", todayOverMin);
        dayResult.put("processCheck", processeCheckResult);
        return dayResult;
    }

    private void processRules(String information, List<SeaWeatherVo> hoursWeather, Map<String, String> alarms, Map<String, Double> alarmCondition, Map<String, String> alarmRule, Map<String, Set<Integer>> alarmDuration, JSONObject checkInfo) {
        JSONArray rules = JSONObject.parseArray(information);
        for (SeaWeatherVo weather : hoursWeather) {
            WeatherProcessVo vo = new WeatherProcessVo();
            BeanUtils.copyProperties(weather, vo);
            boolean goodDay = true;
            if (rules != null && !rules.isEmpty()) {
                for (Object o : rules) {
                    JSONObject processRule = (JSONObject) o;
                    String ruleName = processRule.getString("code");
                    Integer ruleState = processRule.getInteger("state");
                    //风速
                    if (WeatherConditionEnum.WIND_SPEED.getCode().equals(ruleName)) {
                        vo.setWindCondition(processRule.getString("data"));
                        if (ruleState == 1) {
                            JSONArray ruleArray = processRule.getJSONArray("data");
                            BigDecimal val = weather.getWindSpeed();
                            boolean allow = this.processRule(WeatherConditionEnum.WIND_SPEED, ruleArray, val.doubleValue(), alarms, alarmCondition, alarmRule);
                            if (!allow) {
                                Set<Integer> set = alarmDuration.get(WeatherConditionEnum.WIND_SPEED.getCode());
                                if (set == null) {
                                    set = new HashSet<>();
                                    set.add(weather.getHour());
                                    alarmDuration.put(WeatherConditionEnum.WIND_SPEED.getCode(), set);
                                } else {
                                    set.add(weather.getHour());
                                }
                                goodDay = false;
                                vo.setWindCheck(false);
                            } else {
                                vo.setWindCheck(true);
                            }
                        } else {
                            vo.setWindCheck(true);
                        }
                    }
                    //浪高
                    if (WeatherConditionEnum.WAVE_HEIGHT.getCode().equals(ruleName)) {
                        vo.setWaveCondition(processRule.getString("data"));
                        if (ruleState == 1) {
                            JSONArray ruleArray = processRule.getJSONArray("data");
                            double val = weather.getWaveHeight().doubleValue();
                            boolean allow = this.processRule(WeatherConditionEnum.WAVE_HEIGHT, ruleArray, val, alarms, alarmCondition, alarmRule);
                            if (!allow) {
                                Set<Integer> set = alarmDuration.get(WeatherConditionEnum.WAVE_HEIGHT.getCode());
                                if (set == null) {
                                    set = new HashSet<>();
                                    set.add(weather.getHour());
                                    alarmDuration.put(WeatherConditionEnum.WAVE_HEIGHT.getCode(), set);
                                } else {
                                    set.add(weather.getHour());
                                }
                                goodDay = false;
                                vo.setWaveCheck(false);
                            } else {
                                vo.setWaveCheck(true);
                            }
                        } else {
                            vo.setWaveCheck(true);
                        }
                    }
                    //能见度
                    if (WeatherConditionEnum.VISIBILITY.getCode().equals(ruleName)) {
                        vo.setVisibilityCondition(processRule.getString("data"));
                        if (ruleState == 1) {
                            JSONArray ruleArray = processRule.getJSONArray("data");
                            double val = weather.getVisibility().doubleValue();
                            boolean allow = this.processRule(WeatherConditionEnum.VISIBILITY, ruleArray, val, alarms, alarmCondition, alarmRule);
                            if (!allow) {
                                Set<Integer> set = alarmDuration.get(WeatherConditionEnum.VISIBILITY.getCode());
                                if (set == null) {
                                    set = new HashSet<>();
                                    set.add(weather.getHour());
                                    alarmDuration.put(WeatherConditionEnum.VISIBILITY.getCode(), set);
                                } else {
                                    set.add(weather.getHour());
                                }
                                goodDay = false;
                                vo.setVisibilityCheck(false);
                            } else {
                                vo.setVisibilityCheck(true);
                            }
                        } else {
                            vo.setVisibilityCheck(true);
                        }
                    }
                    //降雨
                    if (WeatherConditionEnum.PRECIPITATION.getCode().equals(ruleName)) {
                        vo.setPrecipitationCondition(processRule.getString("data"));
                        if (ruleState == 1) {
                            JSONArray ruleArray = processRule.getJSONArray("data");
                            double val = weather.getPrecipitation().doubleValue();
                            boolean allow = this.processRule(WeatherConditionEnum.PRECIPITATION, ruleArray, val, alarms, alarmCondition, alarmRule);
                            if (!allow) {
                                Set<Integer> set = alarmDuration.get(WeatherConditionEnum.PRECIPITATION.getCode());
                                if (set == null) {
                                    set = new HashSet<>();
                                    set.add(weather.getHour());
                                    alarmDuration.put(WeatherConditionEnum.PRECIPITATION.getCode(), set);
                                } else {
                                    set.add(weather.getHour());
                                }
                                goodDay = false;
                                vo.setPrecipitationCheck(false);
                            } else {
                                vo.setPrecipitationCheck(true);
                            }
                        } else {
                            vo.setPrecipitationCheck(true);
                        }
                    }
                    //温度
                    if (WeatherConditionEnum.TEMPERATURE.getCode().equals(ruleName)) {
                        vo.setTemperatureCondition(processRule.getString("data"));
                        if (ruleState == 1) {
                            JSONArray ruleArray = processRule.getJSONArray("data");
                            double val = weather.getTemperature().doubleValue();
                            boolean allow = this.processRule(WeatherConditionEnum.TEMPERATURE, ruleArray, val, alarms, alarmCondition, alarmRule);
                            if (!allow) {
                                Set<Integer> set = alarmDuration.get(WeatherConditionEnum.TEMPERATURE.getCode());
                                if (set == null) {
                                    set = new HashSet<Integer>();
                                    set.add(weather.getHour());
                                    alarmDuration.put(WeatherConditionEnum.TEMPERATURE.getCode(), set);
                                } else {
                                    set.add(weather.getHour());
                                }
                                goodDay = false;
                                vo.setTemperatureCheck(false);
                            } else {
                                vo.setTemperatureCheck(true);
                            }
                        } else {
                            vo.setTemperatureCheck(true);
                        }
                    }

                }
            }
            vo.setWeatherCheck(goodDay);
            checkInfo.put(weather.getHour().toString().substring(8), vo);
        }
    }

    /**
     * @param weather   某小时的天气预报
     * @param ships     需要对比的船舶
     * @param max       当天的气象指标最大值
     * @param min       当天的气象指标最小值
     * @param overTop   当天的气象指标最超出最大值
     * @param overBoot  当天的气象指标最超出最小值
     * @param checkInfo
     */
    private void processRules2(SeaWeatherVo weather, List<ShipInfo> ships, Map<String, Double> max, Map<String, Double> min, Map<String, Integer> overTop, Map<String, Integer> overBoot, JSONObject checkInfo) {
        double windSpeed = weather.getWindSpeed().doubleValue();
        double waveHeight = weather.getWaveHeight().doubleValue();
        double visibility = weather.getVisibility().doubleValue();
        double temperature = weather.getTemperature().doubleValue();
        double precipitation = weather.getPrecipitation().doubleValue();
        checkInfo.put("windSpeed", windSpeed);
        checkInfo.put("waveHeight", waveHeight);
        checkInfo.put("visibility", visibility);
        checkInfo.put("temperature", temperature);
        checkInfo.put("precipitation", precipitation);
        //
        List<Map<String, String>> windWarning = new ArrayList<>();
        //
        List<Map<String, String>> waveWarning = new ArrayList();
        //
        List<Map<String, String>> visiWarning = new ArrayList();
        //
        List<Map<String, String>> tempWarning = new ArrayList();
        //
        List<Map<String, String>> rainWarning = new ArrayList();
        for (ShipInfo ship : ships) {
            JSONArray rules = JSONObject.parseArray(ship.getInformation());
            if (rules != null && !rules.isEmpty()) {
                for (Object o : rules) {
                    JSONObject processRule = (JSONObject) o;
                    String ruleName = processRule.getString("code");
                    Integer ruleState = processRule.getInteger("state");
                    JSONArray ruleArray = processRule.getJSONArray("data");
                    if (ruleState == 1) {
                        //风速
                        if (WeatherConditionEnum.WIND_SPEED.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, windSpeed, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.WIND_SPEED.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.WIND_SPEED.getCode());
                                windWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.WIND_SPEED.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.WIND_SPEED.getCode());
                                windWarning.add(ill);
                            }
                        }
                        //浪高
                        if (WeatherConditionEnum.WAVE_HEIGHT.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, waveHeight, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.WAVE_HEIGHT.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.WAVE_HEIGHT.getCode());
                                waveWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.WAVE_HEIGHT.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.WAVE_HEIGHT.getCode());
                                waveWarning.add(ill);
                            }
                        }
                        //能见度
                        if (WeatherConditionEnum.VISIBILITY.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, visibility, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.VISIBILITY.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.VISIBILITY.getCode());
                                visiWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.VISIBILITY.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.VISIBILITY.getCode());
                                visiWarning.add(ill);
                            }
                        }
                        //降雨
                        if (WeatherConditionEnum.PRECIPITATION.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, precipitation, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.PRECIPITATION.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.PRECIPITATION.getCode());
                                rainWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.PRECIPITATION.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.PRECIPITATION.getCode());
                                rainWarning.add(ill);
                            }
                        }
                        //温度
                        if (WeatherConditionEnum.TEMPERATURE.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, temperature, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.TEMPERATURE.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.TEMPERATURE.getCode());
                                tempWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.TEMPERATURE.getCode(), 1);
                                ill.put("name", ship.getName());
                                ill.put("condition", WeatherConditionEnum.TEMPERATURE.getCode());
                                tempWarning.add(ill);
                            }
                        }
                    }
                }
            }
        }
        if (windWarning.isEmpty()) {
            checkInfo.put("windSpeedCheck", true);
        } else {
            checkInfo.put("windSpeedCheck", false);
            checkInfo.put("windSpeedWarn", windWarning);
        }
        if (waveWarning.isEmpty()) {
            checkInfo.put("waveHeightCheck", true);
        } else {
            checkInfo.put("waveHeightCheck", false);
            checkInfo.put("waveHeightWarn", waveWarning);
        }
        if (visiWarning.isEmpty()) {
            checkInfo.put("visibilityCheck", true);
        } else {
            checkInfo.put("visibilityCheck", false);
            checkInfo.put("visibilityWarn", visiWarning);
        }
        if (tempWarning.isEmpty()) {
            checkInfo.put("temperatureCheck", true);
        } else {
            checkInfo.put("temperatureCheck", false);
            checkInfo.put("temperatureWarn", tempWarning);
        }
        if (rainWarning.isEmpty()) {
            checkInfo.put("precipitationCheck", true);
        } else {
            checkInfo.put("precipitationCheck", false);
            checkInfo.put("precipitationWarn", rainWarning);
        }

        if (max.get(WeatherConditionEnum.WIND_SPEED.getCode()) == null || max.get(WeatherConditionEnum.WIND_SPEED.getCode()).doubleValue() < windSpeed) {
            max.put(WeatherConditionEnum.WIND_SPEED.getCode(), windSpeed);
        }
        if (min.get(WeatherConditionEnum.WIND_SPEED.getCode()) == null || min.get(WeatherConditionEnum.WIND_SPEED.getCode()).doubleValue() > windSpeed) {
            min.put(WeatherConditionEnum.WIND_SPEED.getCode(), windSpeed);
        }

        if (max.get(WeatherConditionEnum.WAVE_HEIGHT.getCode()) == null || max.get(WeatherConditionEnum.WAVE_HEIGHT.getCode()).doubleValue() < waveHeight) {
            max.put(WeatherConditionEnum.WAVE_HEIGHT.getCode(), waveHeight);
        }
        if (min.get(WeatherConditionEnum.WAVE_HEIGHT.getCode()) == null || min.get(WeatherConditionEnum.WAVE_HEIGHT.getCode()).doubleValue() > waveHeight) {
            min.put(WeatherConditionEnum.WAVE_HEIGHT.getCode(), waveHeight);
        }
        if (max.get(WeatherConditionEnum.VISIBILITY.getCode()) == null || max.get(WeatherConditionEnum.VISIBILITY.getCode()).doubleValue() < visibility) {
            max.put(WeatherConditionEnum.VISIBILITY.getCode(), visibility);
        }
        if (min.get(WeatherConditionEnum.VISIBILITY.getCode()) == null || min.get(WeatherConditionEnum.VISIBILITY.getCode()).doubleValue() > visibility) {
            min.put(WeatherConditionEnum.VISIBILITY.getCode(), visibility);
        }
        if (max.get(WeatherConditionEnum.PRECIPITATION.getCode()) == null || max.get(WeatherConditionEnum.PRECIPITATION.getCode()).doubleValue() < precipitation) {
            max.put(WeatherConditionEnum.PRECIPITATION.getCode(), precipitation);
        }
        if (min.get(WeatherConditionEnum.PRECIPITATION.getCode()) == null || min.get(WeatherConditionEnum.PRECIPITATION.getCode()).doubleValue() > precipitation) {
            min.put(WeatherConditionEnum.PRECIPITATION.getCode(), precipitation);
        }
        if (max.get(WeatherConditionEnum.TEMPERATURE.getCode()) == null || max.get(WeatherConditionEnum.TEMPERATURE.getCode()).doubleValue() < temperature) {
            max.put(WeatherConditionEnum.TEMPERATURE.getCode(), temperature);
        }
        if (min.get(WeatherConditionEnum.TEMPERATURE.getCode()) == null || min.get(WeatherConditionEnum.TEMPERATURE.getCode()).doubleValue() > temperature) {
            min.put(WeatherConditionEnum.TEMPERATURE.getCode(), temperature);
        }
    }

    /**
     * @param weather   某小时的天气预报
     * @param processes 需要对比的工序
     * @param max       当天的气象指标最大值
     * @param min       当天的气象指标最小值
     * @param overTop   当天的气象指标最超出最大值
     * @param overBoot  当天的气象指标最超出最小值
     * @param checkInfo
     */
    private void processRules3(SeaWeatherVo weather, List<ProjectProcess> processes, Map<String, Double> max, Map<String, Double> min, Map<String, Integer> overTop, Map<String, Integer> overBoot, JSONObject checkInfo) {
        double windSpeed = weather.getWindSpeed().doubleValue();
        double waveHeight = weather.getWaveHeight().doubleValue();
        double visibility = weather.getVisibility().doubleValue();
        double temperature = weather.getTemperature().doubleValue();
        double precipitation = weather.getPrecipitation().doubleValue();
        checkInfo.put("windSpeed", windSpeed);
        checkInfo.put("waveHeight", waveHeight);
        checkInfo.put("visibility", visibility);
        checkInfo.put("temperature", temperature);
        checkInfo.put("precipitation", precipitation);
        //
        List<Map<String, String>> windWarning = new ArrayList<>();
        //
        List<Map<String, String>> waveWarning = new ArrayList();
        //
        List<Map<String, String>> visiWarning = new ArrayList();
        //
        List<Map<String, String>> tempWarning = new ArrayList();
        //
        List<Map<String, String>> rainWarning = new ArrayList();
        for (ProjectProcess item : processes) {
            JSONArray rules = JSONObject.parseArray(item.getInformation());
            if (rules != null && !rules.isEmpty()) {
                for (Object o : rules) {
                    JSONObject processRule = (JSONObject) o;
                    String ruleName = processRule.getString("code");
                    Integer ruleState = processRule.getInteger("state");
                    JSONArray ruleArray = processRule.getJSONArray("data");
                    if (ruleState == 1) {
                        //风速
                        if (WeatherConditionEnum.WIND_SPEED.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, windSpeed, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.WIND_SPEED.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.WIND_SPEED.getCode());
                                windWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.WIND_SPEED.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.WIND_SPEED.getCode());
                                windWarning.add(ill);
                            }
                        }
                        //浪高
                        if (WeatherConditionEnum.WAVE_HEIGHT.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, waveHeight, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.WAVE_HEIGHT.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.WAVE_HEIGHT.getCode());
                                waveWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.WAVE_HEIGHT.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.WAVE_HEIGHT.getCode());
                                waveWarning.add(ill);
                            }
                        }
                        //能见度
                        if (WeatherConditionEnum.VISIBILITY.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, visibility, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.VISIBILITY.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.VISIBILITY.getCode());
                                visiWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.VISIBILITY.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.VISIBILITY.getCode());
                                visiWarning.add(ill);
                            }
                        }
                        //降雨
                        if (WeatherConditionEnum.PRECIPITATION.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, precipitation, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.PRECIPITATION.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.PRECIPITATION.getCode());
                                rainWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.PRECIPITATION.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.PRECIPITATION.getCode());
                                rainWarning.add(ill);
                            }
                        }
                        //温度
                        if (WeatherConditionEnum.TEMPERATURE.getCode().equals(ruleName)) {
                            Map ill = new HashMap();
                            int allow = this.processRule2(ruleArray, temperature, ill);
                            if (allow == 2) {
                                overTop.put(WeatherConditionEnum.TEMPERATURE.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.TEMPERATURE.getCode());
                                tempWarning.add(ill);
                            } else if (allow == 1) {
                                overBoot.put(WeatherConditionEnum.TEMPERATURE.getCode(), 1);
                                ill.put("name", item.getName());
                                ill.put("condition", WeatherConditionEnum.TEMPERATURE.getCode());
                                tempWarning.add(ill);
                            }
                        }
                    }
                }
            }
        }
        if (windWarning.isEmpty()) {
            checkInfo.put("windSpeedCheck", true);
        } else {
            checkInfo.put("windSpeedCheck", false);
            checkInfo.put("windSpeedWarn", windWarning);
        }
        if (waveWarning.isEmpty()) {
            checkInfo.put("waveHeightCheck", true);
        } else {
            checkInfo.put("waveHeightCheck", false);
            checkInfo.put("waveHeightWarn", waveWarning);
        }
        if (visiWarning.isEmpty()) {
            checkInfo.put("visibilityCheck", true);
        } else {
            checkInfo.put("visibilityCheck", false);
            checkInfo.put("visibilityWarn", visiWarning);
        }
        if (tempWarning.isEmpty()) {
            checkInfo.put("temperatureCheck", true);
        } else {
            checkInfo.put("temperatureCheck", false);
            checkInfo.put("temperatureWarn", tempWarning);
        }
        if (rainWarning.isEmpty()) {
            checkInfo.put("precipitationCheck", true);
        } else {
            checkInfo.put("precipitationCheck", false);
            checkInfo.put("precipitationWarn", rainWarning);
        }

        if (max.get(WeatherConditionEnum.WIND_SPEED.getCode()) == null || max.get(WeatherConditionEnum.WIND_SPEED.getCode()).doubleValue() < windSpeed) {
            max.put(WeatherConditionEnum.WIND_SPEED.getCode(), windSpeed);
        }
        if (min.get(WeatherConditionEnum.WIND_SPEED.getCode()) == null || min.get(WeatherConditionEnum.WIND_SPEED.getCode()).doubleValue() > windSpeed) {
            min.put(WeatherConditionEnum.WIND_SPEED.getCode(), windSpeed);
        }

        if (max.get(WeatherConditionEnum.WAVE_HEIGHT.getCode()) == null || max.get(WeatherConditionEnum.WAVE_HEIGHT.getCode()).doubleValue() < waveHeight) {
            max.put(WeatherConditionEnum.WAVE_HEIGHT.getCode(), waveHeight);
        }
        if (min.get(WeatherConditionEnum.WAVE_HEIGHT.getCode()) == null || min.get(WeatherConditionEnum.WAVE_HEIGHT.getCode()).doubleValue() > waveHeight) {
            min.put(WeatherConditionEnum.WAVE_HEIGHT.getCode(), waveHeight);
        }
        if (max.get(WeatherConditionEnum.VISIBILITY.getCode()) == null || max.get(WeatherConditionEnum.VISIBILITY.getCode()).doubleValue() < visibility) {
            max.put(WeatherConditionEnum.VISIBILITY.getCode(), visibility);
        }
        if (min.get(WeatherConditionEnum.VISIBILITY.getCode()) == null || min.get(WeatherConditionEnum.VISIBILITY.getCode()).doubleValue() > visibility) {
            min.put(WeatherConditionEnum.VISIBILITY.getCode(), visibility);
        }
        if (max.get(WeatherConditionEnum.PRECIPITATION.getCode()) == null || max.get(WeatherConditionEnum.PRECIPITATION.getCode()).doubleValue() < precipitation) {
            max.put(WeatherConditionEnum.PRECIPITATION.getCode(), precipitation);
        }
        if (min.get(WeatherConditionEnum.PRECIPITATION.getCode()) == null || min.get(WeatherConditionEnum.PRECIPITATION.getCode()).doubleValue() > precipitation) {
            min.put(WeatherConditionEnum.PRECIPITATION.getCode(), precipitation);
        }
        if (max.get(WeatherConditionEnum.TEMPERATURE.getCode()) == null || max.get(WeatherConditionEnum.TEMPERATURE.getCode()).doubleValue() < temperature) {
            max.put(WeatherConditionEnum.TEMPERATURE.getCode(), temperature);
        }
        if (min.get(WeatherConditionEnum.TEMPERATURE.getCode()) == null || min.get(WeatherConditionEnum.TEMPERATURE.getCode()).doubleValue() > temperature) {
            min.put(WeatherConditionEnum.TEMPERATURE.getCode(), temperature);
        }
    }

    private boolean processRule(WeatherConditionEnum subject, JSONArray ruleArray, Double value, Map<String, String> alarms, Map<String, Double> alarmCondition, Map<String, String> alarmRule) {

        boolean allow = true;
        for (Object o1 : ruleArray) {
            JSONObject ruleItem = (JSONObject) o1;
            String symbol = ruleItem.getString("symbol");
            Double num = ruleItem.getDouble("num");
            if (!StringUtils.isEmpty(symbol) && num != null) {
                if (WeatherRuleEnum.GT.getCode().equals(symbol)) {
                    if (value <= num) {
                        allow = false;
                        alarms.put(subject.getCode(), subject.getName());
                        alarmRule.put(subject.getCode(), WeatherRuleEnum.LTE.getCode());
                        Double con = alarmCondition.get(subject.getCode());
                        if (con == null) {
                            alarmCondition.put(subject.getCode(), value);
                        } else {
                            if (value > con) {
                                alarmCondition.put(subject.getCode(), value);
                            }
                        }
                    }
                } else if (WeatherRuleEnum.GTE.getCode().equals(symbol)) {
                    if (value < num) {
                        allow = false;
                        alarms.put(subject.getCode(), subject.getName());
                        alarmRule.put(subject.getCode(), WeatherRuleEnum.LT.getCode());
                        Double con = alarmCondition.get(subject.getCode());
                        if (con == null) {
                            alarmCondition.put(subject.getCode(), value);
                        } else {
                            if (value > con) {
                                alarmCondition.put(subject.getCode(), value);
                            }
                        }
                    }
                } else if (WeatherRuleEnum.LT.getCode().equals(symbol)) {
                    if (value >= num) {
                        allow = false;
                        alarms.put(subject.getCode(), subject.getName());
                        alarmRule.put(subject.getCode(), WeatherRuleEnum.GTE.getCode());
                        Double con = alarmCondition.get(subject.getCode());
                        if (con == null) {
                            alarmCondition.put(subject.getCode(), value);
                        } else {
                            if (value < con) {
                                alarmCondition.put(subject.getCode(), value);
                            }
                        }
                    }
                } else if (WeatherRuleEnum.LTE.getCode().equals(symbol)) {
                    if (value > num) {
                        allow = false;
                        alarms.put(subject.getCode(), subject.getName());
                        alarmRule.put(subject.getCode(), WeatherRuleEnum.GT.getCode());
                        Double con = alarmCondition.get(subject.getCode());
                        if (con == null) {
                            alarmCondition.put(subject.getCode(), value);
                        } else {
                            if (value < con) {
                                alarmCondition.put(subject.getCode(), value);
                            }
                        }
                    }
                }
            } else {
                //第一条件未填写，第二条件无效
                break;
            }
        }
        return allow;
    }

    private int processRule2(JSONArray ruleArray, double value, Map warnings) {
        int allow = 0;
        for (Object o1 : ruleArray) {
            JSONObject ruleItem = (JSONObject) o1;
            String symbol = ruleItem.getString("symbol");
            Double num = ruleItem.getDouble("num");
            if (!StringUtils.isEmpty(symbol) && num != null) {
                if (WeatherRuleEnum.GT.getCode().equals(symbol)) {
                    if (value <= num) {
                        allow = 1;
                        warnings.put("symbol", symbol);
                        warnings.put("num", num);
                        break;
                    }
                } else if (WeatherRuleEnum.GTE.getCode().equals(symbol)) {
                    if (value < num) {
                        allow = 1;
                        warnings.put("symbol", symbol);
                        warnings.put("num", num);
                        break;
                    }
                } else if (WeatherRuleEnum.LT.getCode().equals(symbol)) {
                    if (value >= num) {
                        allow = 2;
                        warnings.put("symbol", symbol);
                        warnings.put("num", num);
                        break;
                    }
                } else if (WeatherRuleEnum.LTE.getCode().equals(symbol)) {
                    if (value > num) {
                        allow = 2;
                        warnings.put("symbol", symbol);
                        warnings.put("num", num);
                        break;
                    }
                }
            } else {
                //第一条件未填写，第二条件无效
                break;
            }
        }
        return allow;
    }


    private static String getDayOfWeekText(int dayOfWeek) {
        switch (dayOfWeek) {
            case Calendar.SUNDAY:
                return "周日";
            case Calendar.MONDAY:
                return "周一";
            case Calendar.TUESDAY:
                return "周二";
            case Calendar.WEDNESDAY:
                return "周三";
            case Calendar.THURSDAY:
                return "周四";
            case Calendar.FRIDAY:
                return "周五";
            case Calendar.SATURDAY:
                return "周六";
            default:
                return "";
        }
    }
}
