package com.jinghe.breeze.modules.sea.service.impl;

import com.jinghe.breeze.modules.sea.entity.ExtremeWeather;
import com.jinghe.breeze.modules.sea.mapper.ExtremeWeatherMapper;
import com.jinghe.breeze.modules.sea.service.IExtremeWeatherService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 极端天气
 * @Author: jeecg-boot
 * @Date:   2024-06-07
 * @Version: V1.0
 */
@Service
public class ExtremeWeatherServiceImpl extends ServiceImpl<ExtremeWeatherMapper, ExtremeWeather> implements IExtremeWeatherService {

}
