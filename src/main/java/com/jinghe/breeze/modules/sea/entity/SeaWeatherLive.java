package com.jinghe.breeze.modules.sea.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: sea_weather_live
 * @Author: jeecg-boot
 * @Date:   2024-04-28
 * @Version: V1.0
 */
@Data
@TableName("sea_weather_live")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sea_weather_live对象", description="sea_weather_live")
public class SeaWeatherLive implements Serializable {
    private static final long serialVersionUID = 1L;

	/**标识*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private java.lang.String id;
	/**小时*/
	@Excel(name = "小时", width = 15)
    @ApiModelProperty(value = "小时")
    private Integer hour;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**删除标识*/
	@Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer deleteFlag;
	/**城市名称*/
	@Excel(name = "城市名称", width = 15)
    @ApiModelProperty(value = "城市名称")
    private java.lang.String cityName;
    /**纬度*/
    @TableField(exist=false)
    @ApiModelProperty(value = "纬度")
    private java.lang.String lat;
    /**经度*/
    @TableField(exist=false)
    @ApiModelProperty(value = "经度")
    private java.lang.String lon;
	/**数据更新时间*/
	@Excel(name = "数据更新时间", width = 20, format = "yyyy-MM-dd HH:mm")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "数据更新时间")
    private java.util.Date dateTime;
	/**天气现象*/
	@Excel(name = "天气现象", width = 15)
    @ApiModelProperty(value = "天气现象")
    private java.lang.String weatherDescription;
	/**昼夜*/
	@Excel(name = "昼夜", width = 15)
    @ApiModelProperty(value = "昼夜")
    private java.lang.String pod;
	/**能见度（km）*/
	@Excel(name = "能见度（km）", width = 15)
    @ApiModelProperty(value = "能见度（km）")
    private BigDecimal visibility;
	/**天气代码*/
	@Excel(name = "天气代码", width = 15)
    @ApiModelProperty(value = "天气代码")
    private Integer weatherCode;
	/**云层量（%）*/
	@Excel(name = "云层量（%）", width = 15)
    @ApiModelProperty(value = "云层量（%）")
    private Integer clouds;
	/**气压（mb）*/
	@Excel(name = "气压（mb）", width = 15)
    @ApiModelProperty(value = "气压（mb）")
    private BigDecimal pressure;
	/**降雨量（mm/hr）*/
	@Excel(name = "降雨量（mm/hr）", width = 15)
    @ApiModelProperty(value = "降雨量（mm/hr）")
    private BigDecimal precipitation;
	/**降雪量（mm/hr）*/
	@Excel(name = "降雪量（mm/hr）", width = 15)
    @ApiModelProperty(value = "降雪量（mm/hr）")
    private BigDecimal snow;
	/**温度（℃）*/
	@Excel(name = "温度（℃）", width = 15)
    @ApiModelProperty(value = "温度（℃）")
    private BigDecimal temperature;
	/**体感温度（℃）*/
	@Excel(name = "体感温度（℃）", width = 15)
    @ApiModelProperty(value = "体感温度（℃）")
    private BigDecimal appTemp;
	/**紫外线指数*/
	@Excel(name = "紫外线指数", width = 15)
    @ApiModelProperty(value = "紫外线指数")
    private BigDecimal ultravioletRays;
	/**相对湿度（%）*/
	@Excel(name = "相对湿度（%）", width = 15)
    @ApiModelProperty(value = "相对湿度（%）")
    private BigDecimal relaHumidity;
	/**风向角度度数*/
	@Excel(name = "风向角度度数", width = 15)
    @ApiModelProperty(value = "风向角度度数")
    private BigDecimal windDirection;
	/**风速（m/s）*/
	@Excel(name = "风速（m/s）", width = 15)
    @ApiModelProperty(value = "风速（m/s）")
    private BigDecimal windSpeed;
	/**风向描述*/
	@Excel(name = "风向描述", width = 15)
    @ApiModelProperty(value = "风向描述")
    private java.lang.String windMark;
	/**降雨概率*/
	@Excel(name = "降雨概率", width = 15)
    @ApiModelProperty(value = "降雨概率")
    private BigDecimal probability;
	/**seaPressure*/
	@Excel(name = "seaPressure", width = 15)
    @ApiModelProperty(value = "seaPressure")
    private BigDecimal seaPressure;
	/**snowDepth*/
	@Excel(name = "snowDepth", width = 15)
    @ApiModelProperty(value = "snowDepth")
    private BigDecimal snowDepth;
    //浪高
    private BigDecimal waveHeight;
}
