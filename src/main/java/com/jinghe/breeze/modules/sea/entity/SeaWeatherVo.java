package com.jinghe.breeze.modules.sea.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sea_weather对象", description="海洋天气")
public class SeaWeatherVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**标识*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "标识")
    private String id;

    private Integer day;

    private String dataTime;//数据更新时间

    private String title;
    private Integer hour;
    private Long timestamp;
    private String cityName;//城市名
    private BigDecimal visibility;//能见度 km
    private String weatherDescription;//天气现象
    private Integer weatherCode;//天气代码 参考【海洋天气对照表】菜单
    private Integer clouds;//云层量 %
    private BigDecimal pressure;//气压 mb
    private BigDecimal seaPressure;//海平面气压
    private BigDecimal probability;//降雨概率%
    private BigDecimal precipitation;//降雨量 mm/hr
    private BigDecimal snow;//降雪量 mm/hr
    private BigDecimal snowDepth;//降雪厚度
    private BigDecimal temperature;//实时温度 摄氏度℃
    private BigDecimal minTemperature;//最低温度 摄氏度℃
    private BigDecimal maxTemperature;//最高温度 摄氏度℃
    private BigDecimal lowTemperature;//夜间最低温度 摄氏度℃
    private BigDecimal highTemperature;//白天最高温度 摄氏度℃
    private BigDecimal averageTemperature;//平局温度 摄氏度℃
    private BigDecimal appTemp;//实时体感温度 摄氏度℃
    private BigDecimal minAppTemp;//最低体感温度 摄氏度℃
    private BigDecimal maxAppTemp;//最高体感温度 摄氏度℃
    private BigDecimal ultravioletRays;//紫外线指数 范围0-11
    private BigDecimal relaHumidity;//相对湿度 %
//    private String windDirection;//风向角度度数
//    private String windSpeed;//风速 m/s
//    private String windMark;//风向描述


    //海温
    private BigDecimal sst;
    private BigDecimal sstMin;
    private BigDecimal sstMax;

    //水流速度
    private BigDecimal waterSpeed;
    private BigDecimal waterSpeedMin;
    private BigDecimal waterSpeedMax;

    //水流方向
    private BigDecimal waterDirection;

    //浪高
    private BigDecimal waveHeight;
    private BigDecimal waveHeightMin;
    private BigDecimal waveHeightMax;

    //浪向
    private BigDecimal waveDirection;

    //浪周期
    private BigDecimal wavePeriod;

    //风速
    private BigDecimal windSpeed;
    private BigDecimal windSpeedMin;
    private BigDecimal windSpeedMax;

    //风向
    private BigDecimal windDirection;
    private String windMark;
}
