package com.jinghe.breeze.modules.sea.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="工序施工评估", description="天气是否适合施工")
public class WeatherProcessVo implements Serializable {
    private static final long serialVersionUID = 1L;


    private boolean weatherCheck;

    @ApiModelProperty(value = "能见度")
    private BigDecimal visibility;//能见度 km
    @ApiModelProperty(value = "能见度判断条件",  example = ">15")
    private String visibilityCondition;
    @ApiModelProperty(value = "判断结果",  example = "true、false")
    private boolean visibilityCheck;
    @ApiModelProperty(value = "预警时长(小时)",  example = "3")
    private String visibilityAlarm;
    private String visibilityAlarmDuration;

    @ApiModelProperty(value = "降雨量")
    private BigDecimal precipitation;//降雨量 mm/hr
    @ApiModelProperty(value = "降雨量判断条件",  example = "<25")
    private String precipitationCondition;
    @ApiModelProperty(value = "判断结果",  example = "true、false")
    private boolean precipitationCheck;
    private String precipitationAlarm;
    @ApiModelProperty(value = "预警时长(小时)",  example = "3")
    private String precipitationAlarmDuration;

    @ApiModelProperty(value = "温度")
    private BigDecimal temperature;//实时温度 摄氏度℃
    @ApiModelProperty(value = "温度判断条件",  example = ">15")
    private String temperatureCondition;
    @ApiModelProperty(value = "判断结果",  example = "true、false")
    private boolean temperatureCheck;
    private String temperatureAlarm;
    @ApiModelProperty(value = "预警时长(小时)",  example = "3")
    private String temperatureAlarmDuration;

    //浪高
    @ApiModelProperty(value = "浪高")
    private BigDecimal waveHeight;
    @ApiModelProperty(value = "浪高判断条件",  example = "<1.1")
    private String waveCondition;
    @ApiModelProperty(value = "判断结果",  example = "true、false")
    private boolean waveCheck;
    private String waveAlarm;
    @ApiModelProperty(value = "预警时长(小时)",  example = "3")
    private String waveAlarmDuration;
    //风速
    @ApiModelProperty(value = "风速")
    private BigDecimal windSpeed;
    @ApiModelProperty(value = "风速判断条件",  example = "<8")
    private String windCondition;
    @ApiModelProperty(value = "判断结果",  example = "true、false")
    private boolean windCheck;
    private String windAlarm;
    @ApiModelProperty(value = "预警时长(小时)",  example = "3")
    private String windAlarmDuration;

}
