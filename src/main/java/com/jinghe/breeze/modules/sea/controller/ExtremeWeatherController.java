package com.jinghe.breeze.modules.sea.controller;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import com.jinghe.breeze.modules.sea.entity.ExtremeWeather;
import com.jinghe.breeze.modules.sea.service.IExtremeWeatherService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 极端天气
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
@Api(tags = "极端天气")
@RestController
@RequestMapping("/sea/extremeWeather")
@Slf4j
public class ExtremeWeatherController extends JeecgController<ExtremeWeather, IExtremeWeatherService> {
    @Autowired
    private IExtremeWeatherService extremeWeatherService;

    /**
     * 分页列表查询
     *
     * @param extremeWeather
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "极端天气-分页列表查询")
    @ApiOperation(value = "极端天气-分页列表查询", notes = "极端天气-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ExtremeWeather extremeWeather,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<ExtremeWeather> queryWrapper = new QueryWrapper<>();
        if (!StringUtils.isEmpty(extremeWeather.getReleaseDate())) {
            if (System.currentTimeMillis() - extremeWeather.getReleaseDate().getTime() < 86400000
                    && System.currentTimeMillis() - extremeWeather.getReleaseDate().getTime() > 0) {
                queryWrapper.ge("release_date", new Date(System.currentTimeMillis() - 86400000));
            } else {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                queryWrapper.ge("release_date", sdf.format(extremeWeather.getReleaseDate()) + " 00:00:00")
                        .le("release_date", sdf.format(extremeWeather.getReleaseDate()) + " 23:59:59");
            }
        }
//		queryWrapper.like("release_date",extremeWeather.getReleaseDate());
        queryWrapper.orderByDesc("alert_status").orderByDesc("release_date");
        Page<ExtremeWeather> page = new Page<ExtremeWeather>(pageNo, pageSize);
        IPage<ExtremeWeather> pageList = extremeWeatherService.page(page, queryWrapper);
        return Result.OK(pageList);
    }


    /**
     * 添加
     *
     * @param extremeWeather
     * @return
     */
    @AutoLog(value = "极端天气-添加")
    @ApiOperation(value = "极端天气-添加", notes = "极端天气-添加")
    @RequiresPermissions("extremeWeather:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody ExtremeWeather extremeWeather) {
        extremeWeatherService.save(extremeWeather);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param extremeWeather
     * @return
     */
    @AutoLog(value = "极端天气-编辑")
    @ApiOperation(value = "极端天气-编辑", notes = "极端天气-编辑")
    @RequiresPermissions("extremeWeather:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody ExtremeWeather extremeWeather) {
        extremeWeatherService.updateById(extremeWeather);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "极端天气-通过id删除")
    @ApiOperation(value = "极端天气-通过id删除", notes = "极端天气-通过id删除")
    @RequiresPermissions("extremeWeather:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        extremeWeatherService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "极端天气-批量删除")
    @ApiOperation(value = "极端天气-批量删除", notes = "极端天气-批量删除")
    @RequiresPermissions("extremeWeather:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.extremeWeatherService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "极端天气-通过id查询")
    @ApiOperation(value = "极端天气-通过id查询", notes = "极端天气-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ExtremeWeather extremeWeather = extremeWeatherService.getById(id);
        if (extremeWeather == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(extremeWeather);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param extremeWeather
     */
    @RequiresPermissions("extremeWeather:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ExtremeWeather extremeWeather) {
        return super.exportXls(request, extremeWeather, ExtremeWeather.class, "极端天气");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("extremeWeather:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ExtremeWeather.class);
    }

}
