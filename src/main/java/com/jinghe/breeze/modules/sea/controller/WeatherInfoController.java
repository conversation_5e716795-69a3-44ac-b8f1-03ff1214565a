package com.jinghe.breeze.modules.sea.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jinghe.breeze.modules.sea.entity.SeaEnvironmentHour;
import com.jinghe.breeze.modules.sea.entity.SeaWeatherLive;
import com.jinghe.breeze.modules.sea.service.ISeaEnvironmentHourService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* @Description: 船舶信息
* @Author: jeecg-boot
* @Date:   2024-04-28
* @Version: V1.0
*/
@Api(tags="气象信息")
@RestController
@RequestMapping("/sea/weatherInfo")
@Slf4j
public class WeatherInfoController extends JeecgController<SeaEnvironmentHour, ISeaEnvironmentHourService> {
   @Autowired
   private ISeaEnvironmentHourService iService;

   //@RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date date

    @AutoLog(value = "历史天气")
    @ApiOperation(value="历史天气", notes="历史天气")
    @GetMapping(value = "/hisPage")
    public Result<?> hisPage(@RequestParam("start") @DateTimeFormat(pattern="yyyy-MM-dd  HH:mm:ss") Date start,
                             @RequestParam("end") @DateTimeFormat(pattern="yyyy-MM-dd  HH:mm:ss") Date end,
                             @RequestParam(value = "pageNum", defaultValue = "1")  Integer pageNum,
                             @RequestParam(value = "pageSize", defaultValue = "10")  Integer pageSize) {
        IPage<SeaWeatherLive> result = iService.pageByTime(start, end, pageNum, pageSize);
        return Result.OK(result);
    }

   /**
    * 7天预报
    *
    * @param req
    * @param day
    * @return
    */
   @AutoLog(value = "7天预报")
   @ApiOperation(value="7天预报", notes="7天预报")
   @GetMapping(value = "/get7dayInfo")
   public Result<?> get7dayInfo(@RequestParam(value = "day", defaultValue = "0")  Integer day,HttpServletRequest req) {
       Map map = iService.dailyWeather7day(day);
       return Result.OK(map);
   }

    /**
     * 施工评估
     *
     * @param req
     * @return
     */
    @AutoLog(value = "7天施工评估")
    @ApiOperation(value="7天施工评估", notes="安全管理——施工评估与预警")
    @GetMapping(value = "/processAssess")
    public Result<?> processAssess(HttpServletRequest req) {
        List<JSONObject> jsonObjects = iService.processAssess();
        return Result.OK(jsonObjects);
    }

    /**
     * 出航评估
     *
     * @param req
     * @return
     */
    @AutoLog(value = "7天出航评估")
    @ApiOperation(value="7天出航评估", notes="安全管理——出航评估与预警")
    @GetMapping(value = "/sailingAssess")
    public Result<?> sailingAssess(HttpServletRequest req) {
        List<JSONObject> jsonObjects = iService.sailingAssess();
        return Result.OK(jsonObjects);
    }

    /**
     * 施工评估
     *
     * @param req
     * @return
     */
    @AutoLog(value = "7天施工评估APP")
    @ApiOperation(value="7天施工评估APP", notes="7天施工评估APP")
    @GetMapping(value = "/processAssessNew")
    public Result<?> processAssess2(HttpServletRequest req) {
        List<JSONObject> jsonObjects = iService.processAssess2();
        return Result.OK(jsonObjects);
    }

    /**
     * 出航评估
     *
     * @param req
     * @return
     */
    @AutoLog(value = "7天出航评估APP")
    @ApiOperation(value="7天出航评估APP", notes="7天出航评估APP")
    @GetMapping(value = "/sailingAssessNew")
    public Result<?> sailingAssess2(HttpServletRequest req) {
        List<JSONObject> jsonObjects = iService.sailingAssess2();
        return Result.OK(jsonObjects);
    }



}
