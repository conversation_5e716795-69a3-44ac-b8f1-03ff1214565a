package com.jinghe.breeze.modules.education.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghe.breeze.modules.education.dto.PaperRule;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 试卷管理
 * @Author: jeecg-boot
 * @Date: 2025-06-25
 * @Version: V1.0
 */
@Data
@TableName("edu_exam_paper")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "edu_exam_paper对象", description = "试卷管理")
public class EduExamPaper implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;

    /**
     * 删除标识
     */
    @Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.String delFlag;

    /**
     * 试卷名称
     */
    @Excel(name = "试卷名称", width = 15)
    @ApiModelProperty(value = "试卷名称")
    private java.lang.String name;


    /**
     * 关联培训任务
     */
    @Excel(name = "关联培训任务", width = 15, dictTable = "edu_train", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "关联培训任务id数组")
    private java.lang.String train;

    @TableField(exist = false)
    private String trainNames;
    /**
     * 题目标签
     */
    @Excel(name = "题目标签", width = 15, dicCode = "question_tag")
    @Dict(dicCode = "question_tag")
    @ApiModelProperty(value = "题目标签")
    private java.lang.String tags;

    /**
     * 考试时限
     */
    @Excel(name = "考试时限", width = 15)
    @ApiModelProperty(value = "考试时限")
    private Integer duration;

    /**
     * 及格分数
     */
    @Excel(name = "及格分数", width = 15)
    @ApiModelProperty(value = "及格分数")
    private BigDecimal passScore;


    @ApiModelProperty(value = "满分")
    private Integer score;


    @ApiModelProperty(value = "题型配置信息")
    private String rules;

    @TableField(exist = false)
    private List<PaperRule> paperRules;


//    @TableField(exist = false)
//    @ApiModelProperty(value = "详情")
//    private List<EduExamPaperDetail> details;
}
