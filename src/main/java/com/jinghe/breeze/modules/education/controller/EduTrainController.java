package com.jinghe.breeze.modules.education.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jinghe.breeze.modules.education.entity.EduTrain;
import com.jinghe.breeze.modules.education.service.IEduTrainService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 培训管理
 * @Author: jeecg-boot
 * @Date: 2025-06-25
 * @Version: V1.0
 */
@Api(tags = "培训管理")
@RestController
@RequestMapping("/education/eduTrain")
@Slf4j
public class EduTrainController extends JeecgController<EduTrain, IEduTrainService> {
    @Autowired
    private IEduTrainService eduTrainService;

    /**
     * 分页列表查询
     *
     * @param eduTrain
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "培训管理-分页列表查询")
    @ApiOperation(value = "培训管理-分页列表查询", notes = "培训管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(EduTrain eduTrain,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam(name = "rangeStart", required = false) String rangeStart,
                                   @RequestParam(name = "rangeEnd", required = false) String rangeEnd,
                                   HttpServletRequest req) {
        QueryWrapper<EduTrain> queryWrapper = QueryGenerator.initQueryWrapper(eduTrain, req.getParameterMap());
        SimpleDateFormat myFmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (oConvertUtils.isNotEmpty(rangeStart)) {
            try {
                queryWrapper.between("start_time", myFmt.parse(rangeStart), myFmt.parse(rangeEnd));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        Page<EduTrain> page = new Page<EduTrain>(pageNo, pageSize);
        IPage<EduTrain> pageList = eduTrainService.page(page, queryWrapper);
        eduTrainService.enrichDetails(pageList.getRecords());
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param eduTrain
     * @return
     */
    @AutoLog(value = "培训管理-添加")
    @ApiOperation(value = "培训管理-添加", notes = "培训管理-添加")
    @RequiresPermissions("eduTrain:add")
    @PostMapping(value = "/add")
    public Result<?> add(@Validated @RequestBody EduTrain eduTrain) {
        eduTrainService.createOrUpdate(eduTrain);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param eduTrain
     * @return
     */
    @AutoLog(value = "培训管理-编辑")
    @ApiOperation(value = "培训管理-编辑", notes = "培训管理-编辑")
//    @RequiresPermissions("eduTrain:edit")
    @PutMapping(value = "/edit")
    public Result<?> edit(@Validated @RequestBody EduTrain eduTrain) {
        eduTrainService.createOrUpdate(eduTrain);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "培训管理-通过id删除")
    @ApiOperation(value = "培训管理-通过id删除", notes = "培训管理-通过id删除")
    @RequiresPermissions("eduTrain:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        eduTrainService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "培训管理-批量删除")
    @ApiOperation(value = "培训管理-批量删除", notes = "培训管理-批量删除")
    @RequiresPermissions("eduTrain:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.eduTrainService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "培训管理-通过id查询")
    @ApiOperation(value = "培训管理-通过id查询", notes = "培训管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        EduTrain eduTrain = eduTrainService.getById(id);
        if (eduTrain == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(eduTrain);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param eduTrain
     */
    @RequiresPermissions("eduTrain:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EduTrain eduTrain) {
        return super.exportXls(request, eduTrain, EduTrain.class, "培训管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("eduTrain:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EduTrain.class);
    }


    @RequestMapping(value = "sendPaper", method = RequestMethod.POST)
    public Result<?> sendPaper(@RequestBody EduTrain eduTrain) {
        try {
            eduTrainService.sendPaper(eduTrain);
            return Result.OK("发送成功!");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        }
    }

}
