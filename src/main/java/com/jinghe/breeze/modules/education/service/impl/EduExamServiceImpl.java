package com.jinghe.breeze.modules.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.common.enums.QuestionTypeEnum;
import com.jinghe.breeze.modules.education.entity.*;
import com.jinghe.breeze.modules.education.mapper.EduExamMapper;
import com.jinghe.breeze.modules.education.service.*;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.jinghe.breeze.common.enums.ExamStatusEnum;

/**
 * @Description: 学员考试实例表
 * @Author: jeecg-boot
 * @Date: 2025-06-26
 * @Version: V1.0
 */
@Service
public class EduExamServiceImpl extends ServiceImpl<EduExamMapper, EduExam> implements IEduExamService {
    @Autowired
    private IEduExamDetailService eduExamDetailService;

    @Autowired
    private IEduExamPaperService eduExamPaperService;

    @Autowired
    private IPersonInfoService personInfoService;
    @Autowired
    private IEduQuestionService eduQuestionService;

    @Autowired
    private IEduExamPaperDetailService eduExamPaperDetailService;

    @Autowired
    private IEduTrainService eduTrainService;

    @Autowired
    private ISysUserService sysUserService;

    @Override
    public boolean createExam(EduExam eduExam) {
        if (eduExam == null) {
            throw new IllegalArgumentException("数据不完整");
        }
        if (!StringUtils.hasLength(eduExam.getPaperId())) {
            throw new IllegalArgumentException("试卷id不能为空");
        }
        List<EduExam> list = baseMapper.selectList(new LambdaQueryWrapper<>(eduExam)
                .eq(EduExam::getPaperId, eduExam.getPaperId())
                .eq(EduExam::getDelFlag, DelFlagEnum.NORMAL.getType()));
        if (!list.isEmpty()) {
            String errMsg = String.format("用户`%s`已经发送过试卷", eduExam.getPaperId());
            throw new IllegalArgumentException(errMsg);
        }
        eduExam.setStatus(ExamStatusEnum.PENDING.name());
        save(eduExam);
        return true;
    }


    private void checkIsMyExam(EduExam eduExam) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysUser sysUser = sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getId, loginUser.getId()));
        PersonInfo personInfo = personInfoService.getOne(new LambdaQueryWrapper<PersonInfo>()
                .eq(PersonInfo::getUserId, sysUser.getId())
                .eq(PersonInfo::getDelFlag, DelFlagEnum.NORMAL.getType()));
        if (personInfo == null) {
            throw new IllegalArgumentException("只能操作自己的考试");
        }
        if (eduExam == null) {
            throw new IllegalArgumentException("数据不完整");
        }
        if (!StringUtils.hasLength(eduExam.getId())) {
            throw new IllegalArgumentException("考试id不能为空");
        }
        EduExam entity = getById(eduExam.getId());
        if (entity == null) {
            throw new IllegalArgumentException("考试不存在");
        }
        if (!Objects.equals(entity.getPersonId(), personInfo.getId())) {
            throw new IllegalArgumentException("只能操作自己的考试");
        }
    }

    @Override
    public boolean startExam(EduExam eduExam) {
        checkIsMyExam(eduExam);
        EduExam entity = getById(eduExam.getId());
        if (Objects.equals(entity.getStatus(), ExamStatusEnum.FINISHED.name())) {
            throw new IllegalArgumentException("考试已结束,禁止答题");
        }
        eduExam.setStatus(ExamStatusEnum.INPROGRESS.name());
        if (entity.getStartTime() == null) {
            //如果开始时间为空,则设置为当前时间
            eduExam.setStartTime(new Date());
        }
        updateById(eduExam);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitExam(EduExam eduExam) {
        checkIsMyExam(eduExam);
        EduExam entity = this.getById(eduExam.getId());
        if (entity.getStartTime() == null) {
            throw new IllegalArgumentException("错误!考试起始时间缺失,请重试!");
        }
        if (Objects.equals(ExamStatusEnum.FINISHED.name(), entity.getStatus())) {
            throw new IllegalArgumentException("考试已结束,禁止重复提交");
        }
        //修改考试状态
        //修改交卷时间
        //统计总分
        //更新题目的答题次数和错误次数
        eduExam.setStatus(ExamStatusEnum.FINISHED.name());
        eduExam.setEndTime(new Date());
        List<EduExamDetail> details = eduExam.getDetails();
        String paperId = eduExam.getPaperId();
        if (!StringUtils.hasLength(paperId)) {
            throw new IllegalArgumentException("未找到指定试卷!");
        }
        EduExamPaper examPaper = eduExamPaperService.getById(paperId);
        if (examPaper == null) {
            throw new IllegalArgumentException("找不到指定试卷");
        }
        Date startTime = entity.getStartTime();
        Integer duration = examPaper.getDuration();
        Date currentTime = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.add(Calendar.MINUTE, duration);
        Date expectedEndTime = calendar.getTime();
        if (currentTime.after(expectedEndTime)) {
            throw new IllegalArgumentException("考试已超时，交卷失败！");
        }

        List<EduExamPaperDetail> paperDetails = eduExamPaperDetailService.list(new LambdaQueryWrapper<EduExamPaperDetail>()
                .eq(EduExamPaperDetail::getPaperId, paperId)
                .eq(EduExamPaperDetail::getDelFlag, DelFlagEnum.NORMAL.getType()));
        // 创建题目ID到题目对象的映射
        Map<String, EduExamDetail> questionIdEduExamDetailMap = details.stream()
                .filter(eduExamDetail -> eduExamDetail.getQuestionId() != null)
                .collect(Collectors.toMap(EduExamDetail::getQuestionId, e -> e));
        // 计算总分和统计答题情况
        BigDecimal totalScore = BigDecimal.ZERO;
        // 用于统计题目答题情况的映射
        Map<String, Integer> questionAnswerCountMap = new HashMap<>();
        Map<String, Integer> questionErrorCountMap = new HashMap<>();
        for (EduExamPaperDetail paperDetail : paperDetails) {
            String questionId = paperDetail.getQuestionId();
            if (!StringUtils.hasLength(questionId)) {
                continue;
            }
            EduExamDetail examDetail = questionIdEduExamDetailMap.get(questionId);
            String userAnswer = null;
            if (examDetail != null) {
                userAnswer = examDetail.getAnswer();
            } else {
                examDetail = new EduExamDetail();
                examDetail.setScore(paperDetail.getScore());
                examDetail.setAnswer(null);
                examDetail.setQuestionId(paperDetail.getQuestionId());
            }
            AnswerResult answerResult = evaluateAnswer(paperDetail, userAnswer);
            boolean isCorrect = answerResult.isCorrect();
            BigDecimal scoreRatio = answerResult.getScoreRatio();
            BigDecimal earnedScore = paperDetail.getScore().multiply(scoreRatio);
            examDetail.setScore(earnedScore);
            examDetail.setIsCorrect(isCorrect ? 1 : 0);
            examDetail.setExamId(eduExam.getId());
            // 累加总分
            totalScore = totalScore.add(earnedScore);
            // 统计题目的答题次数和错误次数
            if (StringUtils.hasLength(questionId)) {
                // 统计答题次数（每道题答题一次，无论对错）
                questionAnswerCountMap.put(questionId, questionAnswerCountMap.getOrDefault(questionId, 0) + 1);
                // 统计错误次数（只有答错时才增加）
                if (!isCorrect) {
                    questionErrorCountMap.put(questionId, questionErrorCountMap.getOrDefault(questionId, 0) + 1);
                }
            }
        }
        // 设置考试总分
        eduExam.setScore(totalScore); // 如果数据库字段是Integer类型
        updateById(eduExam);
        if (!details.isEmpty()) {
            eduExamDetailService.saveBatch(details);
        }
        updateQuestionStatistics(questionAnswerCountMap, questionErrorCountMap);
    }

    /**
     * 根据题目类型评估答案正确性和得分比例
     *
     * @param paperDetail 题目
     * @param userAnswer  用户答案
     * @return 答案评估结果
     */
    private AnswerResult evaluateAnswer(EduExamPaperDetail paperDetail, String userAnswer) {
        String questionType = paperDetail.getType();
        String correctAnswer = paperDetail.getAnswer();
        if (correctAnswer == null || userAnswer == null || userAnswer.trim().isEmpty()) {
            return new AnswerResult(false, BigDecimal.ZERO);
        }
        String correct = correctAnswer.trim().toUpperCase();
        userAnswer = userAnswer.trim().toUpperCase();
        QuestionTypeEnum type;
        try {
            type = QuestionTypeEnum.valueOf(questionType.toUpperCase());
        } catch (IllegalArgumentException | NullPointerException e) {
            boolean isDefaultMatch = correct.equals(userAnswer);
            return new AnswerResult(isDefaultMatch, isDefaultMatch ? BigDecimal.ONE : BigDecimal.ZERO);
        }
        switch (type) {
            case MULTIPLE:
                return evaluateMultipleChoice(correct, userAnswer);
            case SINGLE:
            case TRUEFALSE:
            default:
                boolean isExactMatch = correct.equals(userAnswer);
                return new AnswerResult(isExactMatch, isExactMatch ? BigDecimal.ONE : BigDecimal.ZERO);
        }

    }

    /**
     * 评估多选题答案
     * 规则：
     * 1. 有错误选项直接判错（得0分）
     * 2. 不选判错（得0分）
     * 3. 少选得一半分
     * 4. 全选对得满分
     */
    private AnswerResult evaluateMultipleChoice(String correctAnswer, String userAnswer) {
        if (userAnswer.isEmpty()) {
            // 不选判错
            return new AnswerResult(false, BigDecimal.ZERO);
        }

        // 将答案转换为字符集合，支持逗号分隔或连续字符
        Set<Character> correctSet = parseAnswerOptions(correctAnswer);
        Set<Character> userSet = parseAnswerOptions(userAnswer);

        // 检查是否有错误选项
        for (Character userOption : userSet) {
            if (!correctSet.contains(userOption)) {
                // 有错误选项直接判错
                return new AnswerResult(false, BigDecimal.ZERO);
            }
        }

        // 没有错误选项，检查是否完全正确
        if (correctSet.equals(userSet)) {
            // 全选对得满分
            return new AnswerResult(true, BigDecimal.ONE);
        } else if (userSet.size() < correctSet.size()) {
            // 少选得一半分
            return new AnswerResult(false, new BigDecimal("0.5"));
        } else {
            // 理论上不会到这里，因为前面已经检查了错误选项
            return new AnswerResult(false, BigDecimal.ZERO);
        }
    }

    /**
     * 解析答案选项为字符集合
     * 支持格式：A,B,C 或 ABC
     */
    private Set<Character> parseAnswerOptions(String answer) {
        Set<Character> options = new HashSet<>();
        if (answer == null || answer.trim().isEmpty()) {
            return options;
        }

        String cleanAnswer = answer.trim().toUpperCase();

        if (cleanAnswer.contains(",")) {
            // 逗号分隔格式：A,B,C
            String[] parts = cleanAnswer.split(",");
            for (String part : parts) {
                String trimmed = part.trim();
                if (!trimmed.isEmpty()) {
                    options.add(trimmed.charAt(0));
                }
            }
        } else {
            // 连续字符格式：ABC
            for (char c : cleanAnswer.toCharArray()) {
                if (Character.isLetter(c)) {
                    options.add(c);
                }
            }
        }

        return options;
    }

    /**
     * 答案评估结果内部类
     */
    private static class AnswerResult {
        private final boolean correct;
        private final BigDecimal scoreRatio;

        public AnswerResult(boolean correct, BigDecimal scoreRatio) {
            this.correct = correct;
            this.scoreRatio = scoreRatio;
        }

        public boolean isCorrect() {
            return correct;
        }

        public BigDecimal getScoreRatio() {
            return scoreRatio;
        }
    }

    /**
     * 批量更新题目的答题次数和错误次数
     *
     * @param questionAnswerCountMap 题目ID -> 答题次数增量的映射
     * @param questionErrorCountMap  题目ID -> 错误次数增量的映射
     */
    private void updateQuestionStatistics(Map<String, Integer> questionAnswerCountMap,
                                          Map<String, Integer> questionErrorCountMap) {
        if (questionAnswerCountMap.isEmpty()) {
            return;
        }

        // 获取所有需要更新的题目ID
        Set<String> questionIds = questionAnswerCountMap.keySet();

        // 查询当前的题目信息
        LambdaQueryWrapper<EduQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EduQuestion::getId, questionIds)
                .eq(EduQuestion::getDelFlag, DelFlagEnum.NORMAL.getType());
        List<EduQuestion> questions = eduQuestionService.list(queryWrapper);

        // 更新每个题目的统计信息
        List<EduQuestion> questionsToUpdate = new ArrayList<>();
        for (EduQuestion question : questions) {
            String questionId = question.getId();

            // 获取当前的答题次数和错误次数
            Integer currentAnswerCount = question.getAnswerCount() != null ? question.getAnswerCount() : 0;
            Integer currentErrorCount = question.getErrorCount() != null ? question.getErrorCount() : 0;

            // 计算新的统计值
            Integer answerIncrement = questionAnswerCountMap.get(questionId);
            Integer errorIncrement = questionErrorCountMap.getOrDefault(questionId, 0);

            if (answerIncrement != null && answerIncrement > 0) {
                question.setAnswerCount(currentAnswerCount + answerIncrement);
                question.setErrorCount(currentErrorCount + errorIncrement);
                questionsToUpdate.add(question);
            }
        }
        // 批量更新题目统计信息
        if (!questionsToUpdate.isEmpty()) {
            eduQuestionService.updateBatchById(questionsToUpdate);
        }
    }

    @Override
    public void fillInfo(List<EduExam> records, boolean includeAnswerAndAnalysis) {
        if (records == null || records.isEmpty()) {
            return;
        }
        List<String> paperIds = records.stream().map(EduExam::getPaperId).collect(Collectors.toList());
        List<EduExamPaper> papers = eduExamPaperService.list(new LambdaQueryWrapper<EduExamPaper>()
                .in(EduExamPaper::getId, paperIds).eq(EduExamPaper::getDelFlag, DelFlagEnum.NORMAL.getType()));
        eduExamPaperService.fillPaperDetails(papers, includeAnswerAndAnalysis);
        Map<String, EduExamPaper> paperMap = papers.stream().collect(Collectors.toMap(EduExamPaper::getId, paper -> paper));
        records.forEach(record -> {
            record.setPaper(paperMap.get(record.getPaperId()));
        });
    }

    public EduExam getExamDetail(EduExam eduExam) {
        if (eduExam == null || eduExam.getId() == null) {
            throw new IllegalArgumentException("数据不完整");
        }
        EduExam entity = this.getById(eduExam.getId());
        if (entity == null) {
            throw new IllegalArgumentException("找不到指定考试");
        }
        if (!Objects.equals(ExamStatusEnum.FINISHED.name(), eduExam.getStatus())) {
            throw new IllegalArgumentException("考试尚未结束,禁止查看答案");
        }
        List<EduExamDetail> details = eduExamDetailService.list(new LambdaQueryWrapper<EduExamDetail>()
                .eq(EduExamDetail::getExamId, eduExam.getId())
                .eq(EduExamDetail::getDelFlag, DelFlagEnum.NORMAL.getType()));
        entity.setDetails(details);
        fillInfo(Collections.singletonList(entity), true);
        return entity;
    }

}
