package com.jinghe.breeze.modules.education.service.impl;

import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.common.enums.ExamStatusEnum;
import com.jinghe.breeze.modules.education.entity.EduExam;
import com.jinghe.breeze.modules.education.entity.EduTrain;
import com.jinghe.breeze.modules.education.entity.EduTrainDetail;
import com.jinghe.breeze.modules.education.mapper.EduTrainMapper;
import com.jinghe.breeze.modules.education.service.IEduExamService;
import com.jinghe.breeze.modules.education.service.IEduTrainDetailService;
import com.jinghe.breeze.modules.education.service.IEduTrainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 培训管理
 * @Author: jeecg-boot
 * @Date: 2025-06-25
 * @Version: V1.0
 */
@Service
public class EduTrainServiceImpl extends ServiceImpl<EduTrainMapper, EduTrain> implements IEduTrainService {
    @Autowired
    private IEduExamService eduExamService;

    @Autowired
    private IEduTrainDetailService eduTrainDetailService;

    @Override
    public void enrichDetails(List<EduTrain> eduTrains) {
        if (eduTrains == null || eduTrains.isEmpty()) {
            return;
        }
        List<String> trainIds = eduTrains.stream().map(EduTrain::getId).collect(Collectors.toList());
        List<EduTrainDetail> details = eduTrainDetailService.list(new LambdaQueryWrapper<EduTrainDetail>()
                .in(EduTrainDetail::getTrainId, trainIds)
                .eq(EduTrainDetail::getDelFlag, DelFlagEnum.NORMAL.getType()));
        Map<String, List<EduTrainDetail>> detailsByTrainId = details.stream().collect(Collectors.groupingBy(EduTrainDetail::getTrainId));
        eduTrains.forEach(train -> {
            train.setDetails(detailsByTrainId.get(train.getId()));
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(EduTrain eduTrain) {
        List<EduTrainDetail> newDetails = eduTrain.getDetails();

        if (StringUtils.hasLength(eduTrain.getId())) {
            // 更新模式：需要对比子表数据的差异
            updateTrainWithDetails(eduTrain, newDetails);
        } else {
            // 新增模式：直接保存主表和子表
            createTrainWithDetails(eduTrain, newDetails);
        }
    }

    /**
     * 新增培训及其详情
     */
    private void createTrainWithDetails(EduTrain eduTrain, List<EduTrainDetail> details) {
        save(eduTrain);
        if (details != null && !details.isEmpty()) {
            details.forEach(detail -> detail.setTrainId(eduTrain.getId()));
            eduTrainDetailService.saveBatch(details);
        }
    }

    /**
     * 更新培训及其详情（对比差异）
     */
    private void updateTrainWithDetails(EduTrain eduTrain, List<EduTrainDetail> newDetails) {
        // 1. 更新主表
        updateById(eduTrain);
        // 2. 查询现有的子表数据
        LambdaQueryWrapper<EduTrainDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduTrainDetail::getTrainId, eduTrain.getId())
                .eq(EduTrainDetail::getDelFlag, DelFlagEnum.NORMAL.getType());
        List<EduTrainDetail> existingDetails = eduTrainDetailService.list(queryWrapper);
        // 3. 对比差异并处理
        processDetailsDifference(eduTrain.getId(), existingDetails, newDetails);
    }

    /**
     * 处理子表数据的差异（新增、更新、删除）
     */
    private void processDetailsDifference(String trainId, List<EduTrainDetail> existingDetails, List<EduTrainDetail> newDetails) {
        // 如果新数据为空，删除所有现有数据
        if (newDetails == null || newDetails.isEmpty()) {
            if (!existingDetails.isEmpty()) {
                List<String> existingIds = existingDetails.stream()
                        .map(EduTrainDetail::getId)
                        .collect(Collectors.toList());
                eduTrainDetailService.removeByIds(existingIds);
            }
            return;
        }

        // 为新数据设置外键
        newDetails.forEach(detail -> detail.setTrainId(trainId));

        // 创建现有数据的ID映射
        Map<String, EduTrainDetail> existingMap = existingDetails.stream()
                .filter(detail -> StringUtils.hasLength(detail.getId()))
                .collect(Collectors.toMap(EduTrainDetail::getId, detail -> detail));

        // 分类处理新数据
        List<EduTrainDetail> toInsert = new ArrayList<>();  // 需要新增的
        List<EduTrainDetail> toUpdate = new ArrayList<>();  // 需要更新的
        Set<String> processedIds = new HashSet<>();         // 已处理的ID

        for (EduTrainDetail newDetail : newDetails) {
            if (StringUtils.hasLength(newDetail.getId()) && existingMap.containsKey(newDetail.getId())) {
                // 有ID且存在于现有数据中 -> 更新
                toUpdate.add(newDetail);
                processedIds.add(newDetail.getId());
            } else {
                // 无ID或ID不存在于现有数据中 -> 新增
                newDetail.setId(null); // 确保ID为空，让数据库自动生成
                toInsert.add(newDetail);
            }
        }

        // 找出需要删除的数据（现有数据中未被处理的）
        List<String> toDeleteIds = existingDetails.stream()
                .map(EduTrainDetail::getId)
                .filter(id -> StringUtils.hasLength(id) && !processedIds.contains(id))
                .collect(Collectors.toList());

        // 执行数据库操作
        if (!toInsert.isEmpty()) {
            eduTrainDetailService.saveBatch(toInsert);
        }

        if (!toUpdate.isEmpty()) {
            eduTrainDetailService.updateBatchById(toUpdate);
        }

        if (!toDeleteIds.isEmpty()) {
            eduTrainDetailService.removeByIds(toDeleteIds);
        }
    }

    @Override
    public void sendPaper(EduTrain eduTrain) {
        String ids = eduTrain.getPersonId();
        String paperId = eduTrain.getPaperId();
        if (ids == null || paperId == null) {
            return;
        }
        String[] idArray = ids.split(",");
        for (String id : idArray) {
            id = id.trim();
            if (id.isEmpty()) {
                continue;
            }
            EduExam eduExam = new EduExam();
            eduExam.setPersonId(id);
            eduExam.setPaperId(paperId);
            eduExam.setTrainId(eduTrain.getId());
            eduExam.setStatus(ExamStatusEnum.PENDING.name());
            eduExamService.createExam(eduExam);
        }
    }
}
