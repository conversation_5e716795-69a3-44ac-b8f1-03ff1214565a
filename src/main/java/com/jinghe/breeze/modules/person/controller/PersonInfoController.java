package com.jinghe.breeze.modules.person.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.CompletableFuture;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.enums.DelFlagEnum;
import com.jinghe.breeze.modules.person.dto.EnrichedLocationDataDTO;
import com.jinghe.breeze.modules.person.dto.TrajectoryDTO;
import com.jinghe.breeze.modules.person.entity.DeviceHelmet;
import com.jinghe.breeze.modules.person.service.IDeviceHelmetService;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: person_info
 * @Author: jeecg-boot
 * @Date: 2024-05-10
 * @Version: V1.0
 */
@Api(tags = "person_info")
@RestController
@RequestMapping("/person/personInfo")
@Slf4j
public class PersonInfoController extends JeecgController<PersonInfo, IPersonInfoService> {
    @Autowired
    private IPersonInfoService personInfoService;

    @Autowired
    private IDeviceHelmetService deviceHelmetService;

    /**
     * 无分页查询倒序
     */
    @ApiOperation(value = "person_info-无分页查询倒序", notes = "person_info-无分页查询倒序")
    @GetMapping(value = "/listNoPage")
    public Result<?> listNoPage(PersonInfo personInfo, HttpServletRequest req) {
        QueryWrapper<PersonInfo> queryWrapper = QueryGenerator.initQueryWrapper(personInfo, req.getParameterMap());
        List<PersonInfo> list = personInfoService.listNoPage(queryWrapper);
        Page<PersonInfo> page = new Page<PersonInfo>(1, list.size());
        page.setRecords(list);
        return Result.OK(page);
    }

    /**
     * 分页列表查询
     *
     * @param personInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "person_info-分页列表查询")
    @ApiOperation(value = "person_info-分页列表查询", notes = "person_info-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(PersonInfo personInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<PersonInfo> queryWrapper = QueryGenerator.initQueryWrapper(personInfo, req.getParameterMap());
        if (!ObjectUtils.isEmpty(personInfo.getAutoInput())) {
            queryWrapper.eq("auto_input", personInfo.getAutoInput());
        } else {
            queryWrapper.eq("auto_input", 2);
        }

        queryWrapper.orderByDesc("create_time");
        Page<PersonInfo> page = new Page<PersonInfo>(pageNo, pageSize);
        IPage<PersonInfo> pageList = personInfoService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param personInfo
     * @return
     */
    @AutoLog(value = "person_info-添加")
    @ApiOperation(value = "person_info-添加", notes = "person_info-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody PersonInfo personInfo) {
        personInfoService.saveInfo(personInfo);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param personInfo
     * @return
     */
    @AutoLog(value = "person_info-编辑")
    @ApiOperation(value = "person_info-编辑", notes = "person_info-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody PersonInfo personInfo) {
        try {
            personInfoService.updatePersonInfo(personInfo);
            return Result.OK("编辑成功!");
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "获取当前人员可选择的安全帽", notes = "获取当前人员可选择的安全帽")
    @PostMapping(value = "/getCanSelectDeviceHelmetList")
    public Result<List<DeviceHelmet>> getCanSelectDeviceHelmetList(@RequestBody PersonInfo personInfo) {
        List<PersonInfo> personInfoList = personInfoService.list(new LambdaQueryWrapper<PersonInfo>()
                .eq(PersonInfo::getDelFlag, DelFlagEnum.NORMAL)
                .ne(personInfo.getId() != null, PersonInfo::getId, personInfo.getId())
                .isNotNull(PersonInfo::getDeviceCode));
        List<String> codeList = personInfoList.stream().map(PersonInfo::getDeviceCode).collect(Collectors.toList());
        LambdaQueryWrapper<DeviceHelmet> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(DeviceHelmet::getDelFlag, DelFlagEnum.NORMAL);
        if (!codeList.isEmpty()) {
            deviceWrapper.notIn(DeviceHelmet::getDeviceCode, codeList);
        }
        List<DeviceHelmet> deviceHelmetList = deviceHelmetService.list(deviceWrapper);
        return Result.OK(deviceHelmetList);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "person_info-通过id删除")
    @ApiOperation(value = "person_info-通过id删除", notes = "person_info-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        personInfoService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "person_info-批量删除")
    @ApiOperation(value = "person_info-批量删除", notes = "person_info-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.personInfoService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "person_info-通过id查询")
    @ApiOperation(value = "person_info-通过id查询", notes = "person_info-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        PersonInfo personInfo = personInfoService.getById(id);
        if (personInfo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(personInfo);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param personInfo
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PersonInfo personInfo) {
        return super.exportXls(request, personInfo, PersonInfo.class, "person_info");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PersonInfo.class);
    }

    @RequestMapping(value = "/queryByUsername", method = RequestMethod.GET)
    public Result<?> queryUnitByUsername() {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        try {
            Map<String, String> map = personInfoService.queryByUsername(sysUser.getUsername());
            return Result.OK(map);
        } catch (JeecgBootException e) {
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "dashboard-人员信息无分页", notes = "dashboard-人员信息无分页")
    @GetMapping(value = "/getPersonInfoNoPage")
    public Result<?> getPersonInfo() {
        return Result.OK(personInfoService.getPersonInfoNoPage());
    }


    @PostMapping(value = "/addToBlackList")
    public Result<?> addToBlackList(@RequestBody PersonInfo personInfo) {
        try {
            personInfoService.addToBlackList(personInfo, true);
            return Result.OK("添加成功");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        }
    }

    @PostMapping(value = "/removeFromBlackList")
    public Result<?> removeFromBlackList(@RequestBody PersonInfo personInfo) {
        try {
            personInfoService.addToBlackList(personInfo, false);
            return Result.OK("添加成功");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取所有人员位置信息
     *
     * @return
     */
    @AutoLog(value = "获取所有人员位置信息")
    @ApiOperation(value = "获取所有人员位置信息", notes = "获取所有人员位置信息")
    @GetMapping(value = "/location/list")
    public Result<?> getPersonnelLocationList() {
        try {
            List<EnrichedLocationDataDTO> locationList = deviceHelmetService.getAllPersonnelLocations();
            return Result.OK(locationList);
        } catch (Exception e) {
            log.error("获取人员位置信息失败: {}", e.getMessage(), e);
            return Result.error("获取人员位置信息失败: " + e.getMessage());
        }
    }

    /**
     * 异步获取人员轨迹回放数据
     *
     * @param personId  人员ID
     * @param startTime 开始时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param endTime   结束时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @return 轨迹数据列表
     */
    //http://1.0.0.0:8090/getTrackPoints?PuName=value1&StartTime=value2&EndTime=value3
    @AutoLog(value = "获取人员轨迹回放数据")
    @ApiOperation(value = "获取人员轨迹回放数据", notes = "获取人员轨迹回放数据，最大时间跨度为一天，时间格式：yyyy-MM-dd HH:mm:ss")
    @GetMapping(value = "/trajectory/replay")
    public CompletableFuture<Result<?>> getPersonTrajectory(
            @RequestParam(name = "personId") String personId,
            @RequestParam(name = "startTime") String startTime,
            @RequestParam(name = "endTime") String endTime) {

        try {
            // 参数验证
            if (personId == null || personId.trim().isEmpty()) {
                return CompletableFuture.completedFuture(Result.error("人员ID不能为空"));
            }
            if (startTime == null || startTime.trim().isEmpty()) {
                return CompletableFuture.completedFuture(Result.error("开始时间不能为空"));
            }
            if (endTime == null || endTime.trim().isEmpty()) {
                return CompletableFuture.completedFuture(Result.error("结束时间不能为空"));
            }

            log.info("获取人员轨迹数据 - 人员ID: {}, 开始时间: {}, 结束时间: {}", personId, startTime, endTime);

            return personInfoService.getPersonTrajectoryAsync(personId, startTime, endTime)
                    .handle((result, ex) -> {
                        if (ex != null) {
                            log.error("获取人员轨迹数据失败 - 人员ID: {}, 错误: {}", personId, ex.getMessage(), ex);
                            return Result.error("获取人员轨迹数据失败: " + ex.getMessage());
                        } else {
                            log.info("成功获取人员轨迹数据 - 人员ID: {}, 轨迹点数量: {}", personId,
                                    result != null ? result.size() : 0);
                            return Result.OK(result);
                        }
                    });
        } catch (Exception e) {
            log.error("轨迹查询参数验证失败 - 人员ID: {}, 错误: {}", personId, e.getMessage(), e);
            return CompletableFuture.completedFuture(Result.error("参数验证失败: " + e.getMessage()));
        }
    }
}
