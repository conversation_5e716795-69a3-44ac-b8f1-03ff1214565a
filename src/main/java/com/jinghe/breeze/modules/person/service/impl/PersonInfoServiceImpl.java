package com.jinghe.breeze.modules.person.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.utils.PinYinUtil;
import com.jinghe.breeze.common.utils.SysDictUtil;
import com.jinghe.breeze.modules.person.entity.PersonInfo;
import com.jinghe.breeze.modules.person.dto.TrajectoryDTO;
import com.jinghe.breeze.modules.person.mapper.PersonInfoMapper;
import com.jinghe.breeze.modules.person.service.IPersonInfoService;
import com.jinghe.breeze.modules.project.entity.ProjectUnit;
import com.jinghe.breeze.modules.project.mapper.ProjectUnitMapper;
import com.jinghe.breeze.modules.project.service.IProjectUnitService;

import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RestClientException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.CompletableFuture;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * @Description: person_info
 * @Author: jeecg-boot
 * @Date: 2024-05-10
 * @Version: V1.0
 */
@Slf4j
@Service
public class PersonInfoServiceImpl extends ServiceImpl<PersonInfoMapper, PersonInfo> implements IPersonInfoService {
    @Autowired
    private ISysUserService sysUserService;
    @Autowired(required = false)
    private ProjectUnitMapper projectUnitMapper;
    @Autowired
    private IProjectUnitService projectUnitService;
    @Autowired
    private SysDictUtil sysDictUtil;
    @Autowired
    private RestTemplate restTemplate;

  private static   final String API_KEY = "YOUR_API_KEY_HERE";
   private static  final String API_URL = "http://*******:8090";
    @Autowired(required = false)
    @Override
    public void saveInfo(PersonInfo personInfo) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getPhone, personInfo.getPhone());
        queryWrapper.eq(SysUser::getDelFlag, Common.delete_flag.OK);
        queryWrapper.last("limit 1");
        SysUser sysUser = sysUserService.getOne(queryWrapper);
        if (!StringUtils.isEmpty(sysUser)) {
            personInfo.setEnterStatus("in");
            personInfo.setUserId(sysUser.getId());
        } else {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("username", personInfo.getUserName());
            jsonObject.put("realname", personInfo.getName());
            jsonObject.put("password", "jh@12345");
            jsonObject.put("phone", personInfo.getPhone());
            jsonObject.put("workNo", personInfo.getCode());
            SysUser sysUser1 = sysUserService.addUser(jsonObject);
            personInfo.setUserId(sysUser1.getId());
        }
        this.save(personInfo);
    }

    @Override
    public Map<String, String> queryByUsername(String username) {
        LambdaQueryWrapper<SysUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(SysUser::getUsername, username);
        SysUser user = sysUserService.getOne(userWrapper);
        if (user == null) {
            throw new JeecgBootException(String.format("未找到您的账号%s,请联系管理员!", username));
        }
        LambdaQueryWrapper<PersonInfo> personWrapper = new LambdaQueryWrapper<>();
        personWrapper.eq(PersonInfo::getUserId, user.getId());
        PersonInfo personInfo = baseMapper.selectList(personWrapper).stream().findFirst().orElse(null);
        if (personInfo == null) {
            throw new JeecgBootException(String.format("未找到与账号`%s`关联的人员信息,请联系管理员!", username));
        }
        LambdaQueryWrapper<ProjectUnit> unitWrapper = new LambdaQueryWrapper<>();

        unitWrapper.eq(ProjectUnit::getId, personInfo.getDepartment());
        ProjectUnit projectUnit = projectUnitMapper.selectOne(unitWrapper);
        if (projectUnit == null) {
            throw new JeecgBootException("未找到您所在的单位,请联系管理员!");
        }
        Map<String, String> map = new HashMap<>();
        map.put("name", personInfo.getName());
        map.put("department", projectUnit.getName());
        map.put("departmentId", projectUnit.getId());
        map.put("code", personInfo.getCode());
        String picture = personInfo.getPicture();
        if (StringUtils.isEmpty(picture)) {
            map.put("picture", null);
        }
        JSONArray jsonArray = null;
        try {
            jsonArray = JSON.parseArray(personInfo.getPicture());
        } catch (Exception e) {
            return map;
        }
        if (!StringUtils.isEmpty(jsonArray) && !jsonArray.isEmpty()) {
            JSONObject jsonObject = (JSONObject) jsonArray.get(0);
            String url = jsonObject.getString("url");
            map.put("picture", url);//加入用户照片
        }
        return map;
//        [{"uid":"vc-upload-1722829071500-20","url":"http://192.168.10.110:8062/api/sys/common/static/qzhf/IMG_7172(20220902-100914)_1722829855841.JPG","name":"IMG_7172(20220902-100914).JPG","size":4851265,"type":"image/jpeg"}]
    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePersonInfo(PersonInfo personInfo) {
        personInfo.setAutoInput(2);
        if (!StringUtils.isEmpty(personInfo.getLeaveDate())) {
            personInfo.setDeviceCode(null); //解除设备绑定关系
            personInfo.setEnterStatus("out");
        } else if (!StringUtils.isEmpty(personInfo.getEnterDate())) {
            personInfo.setEnterStatus("in");
        }
        baseMapper.updateById(personInfo);
        SysUser user = sysUserService.getById(personInfo.getUserId());
        if (user == null) {
            throw new JeecgBootException("未找到对应的账号");
        }
        user.setPhone(personInfo.getPhone());
        user.setWorkNo(personInfo.getCode());
        user.setRealname(personInfo.getName());
        user.setUsername(personInfo.getUserName());
        sysUserService.updateById(user);
    }

    public List<PersonInfo> listNoPage(QueryWrapper<PersonInfo> queryWrapper) {
        return baseMapper.selectList(queryWrapper.orderByDesc("create_time"));
    }

    public LinkedHashMap<String, List<PersonInfo>> getPersonInfoNoPage() {
        LinkedHashMap<String, String> unitTypeDict = sysDictUtil.getValueKeyDict("unit_type", "单位类型");
        LinkedHashMap<String, String> personTypeDict = sysDictUtil.getValueKeyDict("person_type", "人员类型");
        LinkedHashMap<String, String> jogDict = sysDictUtil.getValueKeyDict("job", "岗位");
        LinkedHashMap<String, String> unitDict = new LinkedHashMap<>();
        projectUnitService.list().stream().forEach(c -> unitDict.put(c.getId(), c.getName()));
        QueryWrapper<PersonInfo> queryWrapper = QueryGenerator.initQueryWrapper(new PersonInfo(), null);
        queryWrapper.eq("del_flag", Common.delete_flag.OK);
        queryWrapper.orderByDesc("name");

        List<PersonInfo> personInfoList = baseMapper.selectList(queryWrapper);
        Page<PersonInfo> page = new Page<PersonInfo>(1, personInfoList.size());
        page.setRecords(personInfoList);
        personInfoList = page.getRecords();

        // 隐藏用户身份证和年龄

        for (PersonInfo personInfo : personInfoList) {
            personInfo.setIdCard(null);
            personInfo.setAge(null);
            personInfo.setDepartmentType(unitTypeDict.get(personInfo.getDepartmentType()));
            personInfo.setDepartment(unitDict.get(personInfo.getDepartment()));
            personInfo.setPersonType(personTypeDict.get(personInfo.getPersonType()));
            personInfo.setJob(jogDict.get(personInfo.getJob()));
        }
        LinkedHashMap<String, List<PersonInfo>> map = new LinkedHashMap<>();
        for (PersonInfo personInfo : personInfoList) {
            String firstChar = PinYinUtil.getFirstCharacter(personInfo.getName());
            if (!map.containsKey(firstChar)) {
                map.put(firstChar, new ArrayList<>());
            }
            map.get(firstChar).add(personInfo);
        }
        TreeMap<String, List<PersonInfo>> treeMap = new TreeMap<>(map);
        map = new LinkedHashMap<>(treeMap);
        List<PersonInfo> tmpList = map.getOrDefault("#", null);
        if (tmpList != null) {
            map.remove("#");
            map.put("#", tmpList);
        } // #的放在末尾
        return map;
    }


    public void addToBlackList(PersonInfo personInfo, boolean isBlack) {
        if (personInfo == null || !StringUtils.hasLength(personInfo.getId())) {
            throw new IllegalArgumentException("人员id不能为空");
        }
        PersonInfo entity = getById(personInfo.getId());
        if (entity == null) {
            throw new IllegalArgumentException("找不到指定的记录");
        }
        entity.setIsBlacklist(isBlack ? 1 : 0);
        if (isBlack) {
            entity.setDeviceCode(null);
            entity.setEnterStatus(null);
            entity.setLeaveDate(null);
        }
        updateById(entity);
    }

    @Override
    @Async
    public CompletableFuture<List<TrajectoryDTO>> getPersonTrajectoryAsync(String personId, String startDate, String endDate) {
        try {
            // 参数验证
            if (personId == null || personId.trim().isEmpty()) {
                throw new IllegalArgumentException("人员ID不能为空");
            }
            // 日期格式验证和时间跨度检查
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            // 检查时间跨度不超过一天
            long daysBetween = ChronoUnit.DAYS.between(start, end);
            if (daysBetween > 1) {
                throw new IllegalArgumentException("查询时间跨度不能超过一天");
            }
            if (start.isAfter(end)) {
                throw new IllegalArgumentException("开始日期不能晚于结束日期");
            }
            // 查询人员信息获取设备编号
            PersonInfo personInfo = getById(personId);
            if (personInfo == null) {
                throw new IllegalArgumentException("未找到对应的人员信息");
            }
            String deviceCode = personInfo.getDeviceCode();
            if (deviceCode == null || deviceCode.trim().isEmpty()) {
                throw new IllegalArgumentException("该人员当前未绑定设备");
            }
            // 异步调用外部API获取轨迹数据
            return getTrajectoryFromExternalApiAsync(deviceCode, startDate, endDate);

        } catch (Exception e) {
            log.error("获取人员轨迹数据失败: {}", e.getMessage(), e);
            CompletableFuture<List<TrajectoryDTO>> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    /**
     * 异步调用外部API获取轨迹数据
     *
     * @param deviceCode 设备编号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 轨迹数据列表的CompletableFuture
     */
    private CompletableFuture<List<TrajectoryDTO>> getTrajectoryFromExternalApiAsync(String deviceCode, String startDate, String endDate) {

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 验证时间跨度不超过一天
                validateTimeSpan(startDate, endDate);
                // 构建请求URL - 根据供应商API文档调整参数名
                String requestUrl = String.format("%s/getTrackPoints?PuName=%s&StartTime=%s&EndTime=%s",
                        API_URL, deviceCode, startDate, endDate);
                log.info("调用轨迹API: {}", requestUrl);
                // 构建请求头 - 根据实际需要调整认证方式
                HttpHeaders headers = new HttpHeaders();
                headers.set("Authorization", "Bearer " + API_KEY);
                headers.set("Content-Type", "application/json");
                HttpEntity<String> entity = new HttpEntity<>(headers);
                // 发起HTTP请求
                ResponseEntity<String> response = restTemplate.exchange(
                        requestUrl,
                        HttpMethod.GET,
                        entity,
                        String.class
                );
                // 解析响应数据
                if (response.getStatusCode().is2xxSuccessful()) {
                    return parseTrajectoryResponse(response.getBody());
                } else {
                    log.error("外部轨迹API调用失败，状态码: {}, 响应: {}",
                            response.getStatusCode(), response.getBody());
                    throw new RuntimeException("外部轨迹API调用失败，状态码: " + response.getStatusCode());
                }
            } catch (RestClientException e) {
                log.error("调用外部轨迹API异常，设备: {}, 时间: {} - {}, 错误: {}",
                        deviceCode, startDate, endDate, e.getMessage(), e);
                throw new RuntimeException("调用外部轨迹API异常: " + e.getMessage());
            } catch (Exception e) {
                log.error("轨迹数据处理异常，设备: {}, 错误: {}", deviceCode, e.getMessage(), e);
                throw new RuntimeException("轨迹数据处理异常: " + e.getMessage());
            }
        });
    }

    /**
     * 验证时间跨度不超过一天
     */
    private void validateTimeSpan(String startDate, String endDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);
            long diffInMillis = end.getTime() - start.getTime();
            long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);
            if (diffInDays > 1) {
                throw new IllegalArgumentException("轨迹查询时间跨度不能超过一天");
            }
            if (start.after(end)) {
                throw new IllegalArgumentException("开始时间不能晚于结束时间");
            }
        } catch (ParseException e) {
            throw new IllegalArgumentException("时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }
    }

    /**
     * 解析外部API响应数据
     *
     * @param responseBody 响应体
     * @return 轨迹数据列表
     */
    private List<TrajectoryDTO> parseTrajectoryResponse(String responseBody) {
        List<TrajectoryDTO> trajectoryList = new ArrayList<>();
        try {
            log.debug("解析轨迹响应数据: {}", responseBody);
            // 根据供应商API文档，响应直接是JsonArray格式
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(responseBody);
            // 检查响应是否为数组
            if (rootNode.isArray()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                for (JsonNode pointNode : rootNode) {
                    try {
                        TrajectoryDTO trajectory = new TrajectoryDTO();
                        // 解析经纬度 - API返回的是字符串格式
                        String longitudeStr = pointNode.get("longitude").asText();
                        String latitudeStr = pointNode.get("latitude").asText();
                        trajectory.setLongitude(Double.parseDouble(longitudeStr));
                        trajectory.setLatitude(Double.parseDouble(latitudeStr));

                        // 解析定位时间 - 转换为时间戳
                        String positionTimeStr = pointNode.get("positionTime").asText();
                        Date positionTime = sdf.parse(positionTimeStr);
                        trajectory.setGpstime(positionTime.getTime());
                        // 解析速度 - API返回的是字符串格式
                        String speedStr = pointNode.get("speed").asText();
                        trajectory.setSpeed(Double.parseDouble(speedStr));
                        // 设备编号
                        trajectory.setPuname(pointNode.get("puName").asText());
                        // 接收时间
                        String receiveTimeStr = pointNode.get("receiveTime").asText();
                        trajectory.setReceiveTime(receiveTimeStr);
                        // 定位类型
                        trajectory.setPositionType(pointNode.get("positionType").asText());
                        // 上报模式
                        trajectory.setReportMode(pointNode.get("reportMode").asText());
                        // 设置报警类型为正常（轨迹数据通常不包含报警信息）
                        trajectory.setAlarmtype("0");
                        trajectoryList.add(trajectory);
                    } catch (Exception e) {
                        log.warn("解析单个轨迹点失败，跳过该点: {}, 错误: {}", pointNode.toString(), e.getMessage());
                    }
                }

                // 按时间戳排序
                trajectoryList.sort(Comparator.comparing(TrajectoryDTO::getGpstime));

                log.info("成功解析轨迹点数量: {}", trajectoryList.size());
            } else {
                log.warn("轨迹API响应格式不是数组: {}", responseBody);
            }

        } catch (Exception e) {
            log.error("解析轨迹响应数据失败: {}, 响应内容: {}", e.getMessage(), responseBody, e);
            throw new RuntimeException("解析轨迹响应数据失败: " + e.getMessage());
        }
        return trajectoryList;
    }


}
