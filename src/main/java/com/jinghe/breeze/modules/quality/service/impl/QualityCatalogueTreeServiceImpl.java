package com.jinghe.breeze.modules.quality.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformation;
import com.jinghe.breeze.modules.quality.service.IQualityEvaluationInformationDetailService;
import com.jinghe.breeze.modules.quality.service.IQualityEvaluationInformationService;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.oConvertUtils;
import com.jinghe.breeze.modules.quality.entity.QualityCatalogueTree;
import com.jinghe.breeze.modules.quality.mapper.QualityCatalogueTreeMapper;
import com.jinghe.breeze.modules.quality.service.IQualityCatalogueTreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: quality_catalogue_tree
 * @Author: jeecg-boot
 * @Date: 2025-06-20
 * @Version: V1.0
 */
@Service
public class QualityCatalogueTreeServiceImpl extends ServiceImpl<QualityCatalogueTreeMapper, QualityCatalogueTree> implements IQualityCatalogueTreeService {
    @Autowired
    private IQualityEvaluationInformationService qualityEvaluationInformationService;

    @Override
    public void addQualityCatalogueTree(QualityCatalogueTree qualityCatalogueTree) {
        if (oConvertUtils.isEmpty(qualityCatalogueTree.getPid())) {
            qualityCatalogueTree.setPid(IQualityCatalogueTreeService.ROOT_PID_VALUE);
        } else {
            //如果当前节点父ID不为空 则设置父节点的hasChildren 为1
            QualityCatalogueTree parent = baseMapper.selectById(qualityCatalogueTree.getPid());
            if (parent != null && !"1".equals(parent.getHasChild())) {
                parent.setHasChild("1");
                baseMapper.updateById(parent);
            }
        }
        baseMapper.insert(qualityCatalogueTree);
    }

    @Override
    public void updateQualityCatalogueTree(QualityCatalogueTree qualityCatalogueTree) {
        QualityCatalogueTree entity = this.getById(qualityCatalogueTree.getId());
        if (entity == null) {
            throw new JeecgBootException("未找到对应实体");
        }
        String old_pid = entity.getPid();
        String new_pid = qualityCatalogueTree.getPid();
        if (!old_pid.equals(new_pid)) {
            updateOldParentNode(old_pid);
            if (oConvertUtils.isEmpty(new_pid)) {
                qualityCatalogueTree.setPid(IQualityCatalogueTreeService.ROOT_PID_VALUE);
            }
            if (!IQualityCatalogueTreeService.ROOT_PID_VALUE.equals(qualityCatalogueTree.getPid())) {
                baseMapper.updateTreeNodeStatus(qualityCatalogueTree.getPid(), IQualityCatalogueTreeService.HASCHILD);
            }
        }
        baseMapper.updateById(qualityCatalogueTree);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteQualityCatalogueTree(String id) throws JeecgBootException {
        //查询选中节点下所有子节点一并删除
        id = this.queryTreeChildIds(id);
        if (id.indexOf(",") > 0) {
            StringBuffer sb = new StringBuffer();
            String[] idArr = id.split(",");
            for (String idVal : idArr) {
                if (idVal != null) {
                    QualityCatalogueTree qualityCatalogueTree = this.getById(idVal);
                    String pidVal = qualityCatalogueTree.getPid();
                    //查询此节点上一级是否还有其他子节点
                    List<QualityCatalogueTree> dataList = baseMapper.selectList(new QueryWrapper<QualityCatalogueTree>().eq("pid", pidVal).notIn("id", Arrays.asList(idArr)));
                    if ((dataList == null || dataList.size() == 0) && !Arrays.asList(idArr).contains(pidVal)
                            && !sb.toString().contains(pidVal)) {
                        //如果当前节点原本有子节点 现在木有了，更新状态
                        sb.append(pidVal).append(",");
                    }
                }
            }
            //批量删除节点
            baseMapper.deleteBatchIds(Arrays.asList(idArr));
            //修改已无子节点的标识
            String[] pidArr = sb.toString().split(",");
            for (String pid : pidArr) {
                this.updateOldParentNode(pid);
            }
        } else {
            QualityCatalogueTree qualityCatalogueTree = this.getById(id);
            if (qualityCatalogueTree == null) {
                throw new JeecgBootException("未找到对应实体");
            }
            updateOldParentNode(qualityCatalogueTree.getPid());
            baseMapper.deleteById(id);
        }
    }

    @Override
    public List<QualityCatalogueTree> queryTreeListNoPage(QueryWrapper<QualityCatalogueTree> queryWrapper) {
        List<QualityCatalogueTree> dataList = baseMapper.selectList(queryWrapper);
        Map<String, List<QualityCatalogueTree>> collect = dataList.stream().collect(Collectors.groupingBy(QualityCatalogueTree::getPid));
        // 递归构建树形结构
        List<QualityCatalogueTree> rootNodes = new ArrayList<>();
        for (QualityCatalogueTree node : dataList) {
            if ("0".equals(node.getPid())) {
                buildChildren(node, collect);
                rootNodes.add(node);
            }
        }

        return rootNodes;
    }

    private void buildChildren(QualityCatalogueTree node, Map<String, List<QualityCatalogueTree>> groupedByParentId) {
        List<QualityCatalogueTree> children = groupedByParentId.get(node.getId());
        if (children != null) {
            node.setHasChild("1");
            node.setChildren(children);
            for (QualityCatalogueTree child : children) {
                buildChildren(child, groupedByParentId);
            }
        } else {
            node.setHasChild("0");
        }
    }

    /**
     * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
     *
     * @param pid
     */
    private void updateOldParentNode(String pid) {
        if (!IQualityCatalogueTreeService.ROOT_PID_VALUE.equals(pid)) {
            Integer count = baseMapper.selectCount(new QueryWrapper<QualityCatalogueTree>().eq("pid", pid));
            if (count == null || count <= 1) {
                baseMapper.updateTreeNodeStatus(pid, IQualityCatalogueTreeService.NOCHILD);
            }
        }
    }

    /**
     * 递归查询节点的根节点
     *
     * @param pidVal
     * @return
     */
    private QualityCatalogueTree getTreeRoot(String pidVal) {
        QualityCatalogueTree data = baseMapper.selectById(pidVal);
        if (data != null && !"0".equals(data.getPid())) {
            return this.getTreeRoot(data.getPid());
        } else {
            return data;
        }
    }

    /**
     * 根据id查询所有子节点id
     *
     * @param ids
     * @return
     */
    private String queryTreeChildIds(String ids) {
        //获取id数组
        String[] idArr = ids.split(",");
        StringBuffer sb = new StringBuffer();
        for (String pidVal : idArr) {
            if (pidVal != null) {
                if (!sb.toString().contains(pidVal)) {
                    if (sb.toString().length() > 0) {
                        sb.append(",");
                    }
                    sb.append(pidVal);
                    this.getTreeChildIds(pidVal, sb);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 递归查询所有子节点
     *
     * @param pidVal
     * @param sb
     * @return
     */
    private StringBuffer getTreeChildIds(String pidVal, StringBuffer sb) {
        List<QualityCatalogueTree> dataList = baseMapper.selectList(new QueryWrapper<QualityCatalogueTree>().eq("pid", pidVal));
        if (dataList != null && dataList.size() > 0) {
            for (QualityCatalogueTree tree : dataList) {
                if (!sb.toString().contains(tree.getId())) {
                    sb.append(",").append(tree.getId());
                }
                this.getTreeChildIds(tree.getId(), sb);
            }
        }
        return sb;
    }

    @Override
    public void enrichDetails(List<QualityCatalogueTree> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        List<String> codeList = list.stream().map(QualityCatalogueTree::getPid).collect(Collectors.toList());
        if (codeList.isEmpty()) {
            return;
        }
        List<QualityEvaluationInformation> infoList = qualityEvaluationInformationService.list(new LambdaQueryWrapper<QualityEvaluationInformation>()
                .in(QualityEvaluationInformation::getQbsCode, codeList));
        Map<String, QualityEvaluationInformation> byCode = infoList.stream().collect(Collectors.toMap(QualityEvaluationInformation::getQbsCode, p -> p));
        list.forEach(qualityEvaluationInformation -> {
            qualityEvaluationInformation.setDetail(byCode.get(qualityEvaluationInformation.getCode()));
        });
    }


}
