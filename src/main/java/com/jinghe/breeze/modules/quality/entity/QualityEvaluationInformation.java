package com.jinghe.breeze.modules.quality.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 验评资料
 * @Author: jeecg-boot
 * @Date: 2025-06-27
 * @Version: V1.0
 */
@ApiModel(value = "quality_evaluation_information对象", description = "验评资料")
@Data
@TableName("quality_evaluation_information")
public class QualityEvaluationInformation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * qbs名称
     */
    @Excel(name = "qbs名称", width = 15)
    @ApiModelProperty(value = "qbs名称")
    private String qbsName;
    /**
     * qbs编码
     */
    @Excel(name = "qbs编码", width = 15)
    @ApiModelProperty(value = "qbs编码")
    private String qbsCode;
    /**
     * qbsId
     */
    @Excel(name = "qbsId", width = 15)
    @ApiModelProperty(value = "qbsId")
    private String qbsId;
    /**
     * 验评结果
     */
    @Excel(name = "验评结果", width = 15, dicCode = "evaluation_state")
    @Dict(dicCode = "evaluation_state")
    @ApiModelProperty(value = "验评结果")
    private String evaluationState;
    /**
     * 单位工程量
     */
    @Excel(name = "单位工程量", width = 15)
    @ApiModelProperty(value = "单位工程量")
    private String quantityWork;
    /**
     * 单位工程量单位
     */
    @Excel(name = "单位工程量单位", width = 15, dicCode = "work_unit")
    @Dict(dicCode = "work_unit")
    @ApiModelProperty(value = "单位工程量单位")
    private String quantityWorkUnit;
    /**
     * 施工开始日期
     */
    @Excel(name = "施工开始日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "施工开始日期")
    private Date constructionStartDate;
    /**
     * 施工结束日期
     */
    @Excel(name = "施工结束日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "施工结束日期")
    private Date constructionEndDate;
    /**
     * 施工单位
     */
    @Excel(name = "施工单位", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @ApiModelProperty(value = "施工单位")
    private String constructionUnit;
    /**
     * 图片资料
     */
    @Excel(name = "图片资料", width = 15)
    @ApiModelProperty(value = "图片资料")
    private String pictureInformation;
    /**
     * 视频资料
     */
    @Excel(name = "视频资料", width = 15)
    @ApiModelProperty(value = "视频资料")
    private String videoInformation;
    /**
     * 验评附件资料
     */
    @Excel(name = "验评附件资料", width = 15)
    @ApiModelProperty(value = "验评附件资料")
    private String attachment;

    @ApiModelProperty(value = "归档状态")
    private String archiveStatus;

    @Dict(dicCode = "bpm_status")
    @Excel(name = "审批状态", width = 15)
    @ApiModelProperty(value = "审批状态")
    private String bpmStatus;

    @Dict(dictTable = "person_info", dicCode = "id", dicText = "name")
    @Excel(name = "上报人", width = 15)
    @ApiModelProperty(value = "上报人")
    private String reporter;
    /**
     * 上报人电话
     */
    @Excel(name = "上报人电话", width = 15)
    @ApiModelProperty(value = "上报人电话")
    private String reporterPhone;
    /**
     * 上报人部门
     */
    @Excel(name = "上报人部门", width = 15)
    @ApiModelProperty(value = "上报人部门")
    private String reporterDepart;
    /**
     * 上报日期
     */
    @Excel(name = "上报日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "上报日期")
    private Date reportDate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "验评日期")
    private Date rectificationCompleteDate;

    @TableField(exist = false)
    private List<QualityEvaluationInformationDetail> details;
}
