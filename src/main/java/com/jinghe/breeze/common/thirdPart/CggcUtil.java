package com.jinghe.breeze.common.thirdPart;//package com.jinghe.common.thirdPort;

import com.alibaba.fastjson.JSONObject;
import com.jinghe.breeze.common.utils.HttpClientUtils;
import com.jinghe.breeze.modules.staff.entity.SubProject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.MD5Util;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CggcUtil {

    public static boolean STAFF_SYNCING = false;
    public static boolean ATTEND_SYNCING = false;
    public static boolean SALARY_SYNCING = false;

//    private static final String BASE_URL = "http://gzblw.cggc.cn:1204/HrAPI";
//
//    private static final String KEY = "2spc";
//
//    private static final String SECRET = "c5964c9f-9a57-11ef-bef0-a0000220fe80";
//
//    private static final String PROJECT_ID = "cab3df56-fdee-4408-8276-096673f18c9a";



    /**
     * 查询项目分包商
     *
     * @param
     * @return
     */
    public String projectSubInfo(SubProject project) throws Exception {
        String url = project.getBaseUrl() + "/api/v1/hr/project/subInfo";
        JSONObject param = new JSONObject();
        param.put("projectId", project.getProjectId());
//        generateParam(param);
        String jsonString = param.toJSONString();
        System.out.println("****POST body:"+jsonString);
        String generateUrl = this.generateUrl(url, project);
        return HttpClientUtils.doPost(generateUrl, jsonString);
    }


    /**
     * 查询项目人员
     *
     * @param
     * @return
     */
    public String projectStaff(SubProject project, Integer page, Integer size) throws Exception {
        String url = project.getBaseUrl() + "/api/v1/hr/project/projectStaff";
        JSONObject param = new JSONObject();
        param.put("projectId", project.getProjectId());
        JSONObject pageHelper = new JSONObject();
        pageHelper.put("pageIndex", page);
        pageHelper.put("pageCount", size);
        param.put("pageHelper", pageHelper);
//        generateParam(param);
        String jsonString = param.toJSONString();
        System.out.println("****POST body:"+jsonString);
        String generateUrl = this.generateUrl(url, project);
        System.out.println("****POST url:"+generateUrl);
        return HttpClientUtils.doPost(generateUrl, jsonString);
    }

    /**
     * 查询人员考勤
     *
     * @param
     * @return
     */
    public String staffContract(String staffId, SubProject project) throws Exception {
        String url = project.getBaseUrl() + "/api/v1/hr/staff/contract/query";
        JSONObject param = new JSONObject();
        param.put("staffId", staffId);
        String jsonString = param.toJSONString();
        System.out.println("****POST body:"+jsonString);
        String generateUrl = this.generateUrl(url, project);
        System.out.println("****POST url:"+generateUrl);
        return HttpClientUtils.doPost(generateUrl+"&staffId="+staffId, jsonString);
    }

    /**
     * 查询人员考勤
     *
     * @param
     * @return
     */
    public String staffAttendance(String subCode, String month, SubProject project) throws Exception {
        String url = project.getBaseUrl() + "/api/v1/hr/staff/attendance/query";
        JSONObject param = new JSONObject();
        param.put("projectId", project.getProjectId());
        param.put("subCode", subCode);
        param.put("queryMonth", month + "-01T00:44:03.082Z");
        String jsonString = param.toJSONString();
        System.out.println("****POST body:"+jsonString);
        String generateUrl = this.generateUrl(url, project);
        return HttpClientUtils.doPost(generateUrl, jsonString);
    }

    /**
     * 查询人员考勤
     *
     * @param
     * @return
     */
    public String staffSalary(String subCode, String month, SubProject project) throws Exception {
        String url = project.getBaseUrl() + "/api/v1/hr/staff/salary/query";
        JSONObject param = new JSONObject();
        param.put("projectId", project.getProjectId());
        param.put("subCode", subCode);
        param.put("queryMonth", month + "-01T00:44:03.082Z");
        String jsonString = param.toJSONString();
        System.out.println("****POST body:"+jsonString);
        String generateUrl = this.generateUrl(url, project);
        return HttpClientUtils.doPost(generateUrl, jsonString);
    }



//    private void generateParam(JSONObject param, SubProject project){
//        long stamp = System.currentTimeMillis()/1000;
//        String time = String.valueOf(stamp);
//        String a = KEY + SECRET + stamp;
//        String lowerCase = MD5Util.MD5Encode(a, "UTF-8").toLowerCase();
//        param.put("_k", KEY);
//        param.put("_t", time);
//        param.put("_s", lowerCase);
//    }

    private String generateUrl(String url, SubProject project){
        long stamp = System.currentTimeMillis()/1000;
        String time = String.valueOf(stamp);
        String a = project.getKey() + project.getSecret() + stamp;
        String lowerCase = MD5Util.MD5Encode(a, "UTF-8").toLowerCase();
        return url + "?_k=" +project.getKey()+ "&_t=" +time+ "&_s=" + lowerCase;
    }

}
