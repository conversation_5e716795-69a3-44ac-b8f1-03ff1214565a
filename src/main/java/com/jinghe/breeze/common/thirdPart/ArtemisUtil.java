package com.jinghe.breeze.common.thirdPart;//package com.jinghe.common.thirdPort;

import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class ArtemisUtil {

    private static final String ARTEMIS_PATH = "/artemis";
    public static final String[] CAMERA_CONTROLL_COMMANDS = {
            "LEFT", // 左转
            "RIGHT", // //右转
            "UP", //  上转
            "DOWN", //  下转
            "ZOOM_IN", //  焦距变大
            "ZOOM_OUT", //  焦距变小
            "LEFT_UP", //  左上
            "LEFT_DOWN", //  左下
            "RIGHT_UP", //  右上
            "RIGHT_DOWN", //  右下
            "FOCUS_NEAR", //  焦点前移
            "FOCUS_FAR", //  焦点后移
            "IRIS_ENLARGE", //  光圈扩大
            "IRIS_REDUCE", //  光圈缩小
            "WIPER_SWITCH", //  接通雨刷开关
            "START_RECORD_TRACK", //  开始记录运行轨迹
            "STOP_RECORD_TRACK", //  停止记录运行轨迹
            "START_TRACK", //  开始运行轨迹
            "STOP_TRACK", //  停止运行轨迹；
            "GOTO_PRESET"//到预置点,以下命令presetIndex不可为空：
    };

    public String execute(String api, String param) {
        ArtemisConfig artemisConfig = this.generateConfig();
        final String VechicleDataApi = ARTEMIS_PATH + api;
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", VechicleDataApi);
            }
        };

        try {
            String result = ArtemisHttpUtil.doPostStringArtemis(artemisConfig, path, param, null, null, "application/json");
            return result;
        } catch (Exception e) {
            // Log the exception if necessary
            // e.g., logger.error("Error executing API call", e);
            return null;
        }
    }


    /**
     * 摄像头列表
     *
     * @param
     * @return
     */
    public String cameraList() throws Exception {
        ArtemisConfig artemisConfig = this.generateConfig();
        final String VechicleDataApi = ARTEMIS_PATH + "/api/resource/v2/encodeDevice/search";
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", VechicleDataApi);
            }
        };
        Map<String, Object> param = new HashMap(4);
        param.put("pageNo", "1");
        param.put("pageSize", "100");
        String result = ArtemisHttpUtil.doPostFormArtemis(artemisConfig,
                path, param, null, null, "application/json");
        return result;
    }

    /**
     * 摄像头在线列表
     *
     * @param
     * @return
     */
    public String cameraOnlineList() throws Exception {
        ArtemisConfig artemisConfig = this.generateConfig();
        final String VechicleDataApi = ARTEMIS_PATH + "/api/nms/v1/online/camera/get";
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", VechicleDataApi);
            }
        };
        JSONObject param = new JSONObject();
        param.put("pageNo", 1);
        param.put("pageSize", 500);
        String result = ArtemisHttpUtil.doPostStringArtemis(artemisConfig,
                path, param.toJSONString(), null, null, "application/json");
        return result;
    }

    /**
     * 控制摄像头
     *
     * @param
     * @return
     */
    public String controlCamera(String cameraCode, Integer action, String command, Integer speed) throws Exception {
        ArtemisConfig artemisConfig = this.generateConfig();
        final String VechicleDataApi = ARTEMIS_PATH + "/api/video/v1/ptzs/controlling";
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", VechicleDataApi);
            }
        };
        Map<String, Object> param = new HashMap(6);
        param.put("cameraIndexCode", cameraCode);
        param.put("action", action);
        param.put("command", command);
        param.put("speed", speed);
        String result = ArtemisHttpUtil.doPostFormArtemis(artemisConfig,
                path, param, null, null, "application/json");
        return result;
    }

    /**
     * 预览摄像头
     *
     * @param
     * @return
     */
    public String viewCamera(String cameraCode) throws Exception {
        ArtemisConfig artemisConfig = this.generateConfig();
        final String VechicleDataApi = ARTEMIS_PATH + "/api/video/v2/cameras/previewURLs";
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", VechicleDataApi);
            }
        };
        JSONObject param = new JSONObject();
        param.put("cameraIndexCode", cameraCode);
        param.put("protocol", "hls"); // hik rtsp  rtmp hls ws
//        param.put("transmode","1");
        param.put("streamType", "0");
//        param.put("streamform","ps");
//        param.put("expand","transcode=0");
        String jsonString = param.toJSONString();
//        param.put("streamform","ps"); //    protocol=rtsp时rtp
        String result = ArtemisHttpUtil.doPostStringArtemis(artemisConfig, path, jsonString, null, null, "application/json");
        return result;
    }

    /**
     * 回放摄像头
     *
     * @param
     * @return
     */
    public String reviewCamera(String cameraCode, String start, String end) throws Exception {
        ArtemisConfig artemisConfig = this.generateConfig();
        final String VechicleDataApi = ARTEMIS_PATH + "/api/video/v2/cameras/playbackURLs";
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", VechicleDataApi);
            }
        };
        Map<String, Object> param = new HashMap(8);
        param.put("cameraIndexCode", cameraCode);
        param.put("protocol", "hik"); // hik rtsp  rtmp hls ws
        param.put("transmode", 0);
        //        param.put("streamform","ps"); //    protocol=rtsp时rtp
        param.put("beginTime", "2017-06-15T00:00:00.000+08:00");  //开始时间和结束时间相差不超过三天
        param.put("endTime", "2017-06-15T01:00:00.000+08:00");
        String result = ArtemisHttpUtil.doPostFormArtemis(artemisConfig,
                path, param, null, null, "application/json");
        return result;
    }

    /**
     * 回放摄像头
     *
     * @param
     * @return
     */
    public String eventSubscription(String cameraCode, String start, String end) throws Exception {
        ArtemisConfig artemisConfig = this.generateConfig();
        final String VechicleDataApi = ARTEMIS_PATH + "/api/eventService/v1/eventSubscriptionByEventTypes";
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", VechicleDataApi);
            }
        };
        Map<String, Object> param = new HashMap(4);
        param.put("eventTypes", new int[]{131586, 131587});  //	进入区域	131586   离开区域	131587

        param.put("eventDest", "hik"); // hik rtsp  rtmp hls ws
        String result = ArtemisHttpUtil.doPostFormArtemis(artemisConfig,
                path, param, null, null, "application/json");
        return result;
    }

    private ArtemisConfig generateConfig() {
        ArtemisConfig artemisConfig = new ArtemisConfig();
        artemisConfig.setHost("**************:1443"); //平台（nginx）IP 和端口
        artemisConfig.setAppKey("21984775"); //合作方 key
        artemisConfig.setAppSecret("UIyqtu4tilRdODmfCe6t");//合作方 Secret
        return artemisConfig;
    }


}
