package com.jinghe.breeze.common.listener;

import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.quality.entity.QualityEvaluationInformation;
import com.jinghe.breeze.modules.quality.entity.QualityIssueRectifications;
import com.jinghe.breeze.modules.quality.service.IQualityEvaluationInformationService;
import com.jinghe.breeze.modules.quality.service.IQualityIssueRectificationsService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.jeecg.common.util.SpringContextUtils;

public class QualityEvaluationInformationListener implements ExecutionListener {


    @Override
    public void notify(DelegateExecution execution) throws Exception {
        IQualityEvaluationInformationService qualityEvaluationInformationService = SpringContextUtils.getBean(IQualityEvaluationInformationService.class);
        String businessKey = execution.getBusinessKey();
        QualityEvaluationInformation byId = qualityEvaluationInformationService.getById(businessKey);
        byId.setArchiveStatus(Common.ARCHIVE_STATUS.completed);
        qualityEvaluationInformationService.updateById(byId);
    }
}
