package com.jinghe.breeze.common.listener;

import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.safety.entity.SafeHiddenDangerRecords;
import com.jinghe.breeze.modules.safety.service.ISafeHiddenDangerRecordsService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.bpm.service.ActivitiService;

import javax.annotation.Resource;

public class SafeHiddenDangerRecordsListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        ISafeHiddenDangerRecordsService safeHiddenDangerRecordsService = SpringContextUtils.getBean(ISafeHiddenDangerRecordsService.class);
        String businessKey = execution.getBusinessKey();
        SafeHiddenDangerRecords byId = safeHiddenDangerRecordsService.getById(businessKey);
        byId.setRectificationStatus(Common.RECTIFICATION_STATUS.completed);
        safeHiddenDangerRecordsService.updateById(byId);
    }
}
