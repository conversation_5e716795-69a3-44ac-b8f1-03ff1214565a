package com.jinghe.breeze.common.listener;

import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.modules.quality.entity.QualityIssueRectifications;
import com.jinghe.breeze.modules.quality.service.IQualityIssueRectificationsService;
import com.jinghe.breeze.modules.safety.entity.SafeHiddenDangerRecords;
import com.jinghe.breeze.modules.safety.service.ISafeHiddenDangerRecordsService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.jeecg.common.util.SpringContextUtils;

public class QualityIssueRectificationsListener implements ExecutionListener {
    @Override
    public void notify(DelegateExecution execution) throws Exception {
        IQualityIssueRectificationsService qualityIssueRectificationsService = SpringContextUtils.getBean(IQualityIssueRectificationsService.class);
        String businessKey = execution.getBusinessKey();
        QualityIssueRectifications byId = qualityIssueRectificationsService.getById(businessKey);
        byId.setRectificationStatus(Common.RECTIFICATION_STATUS.completed);
        qualityIssueRectificationsService.updateById(byId);
    }
}
