package com.jinghe.breeze.common.constants;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public interface Common {

    interface TaskFlag {
        String ALARM_TASK_KEY = "task.alarm.key";
        String AMARM_TASK_DEL_KEY = "amarm_task_del_key";
    }

    /**
     * 共用键值
     */
    interface commonality {
        Integer ZERO = 0;

        Integer ONE = 1;

        Integer TWO = 2;

        Integer THREE = 3;
    }

    interface belong {
        /**
         * 内部
         */
        String INTERIOR = "interior";
        /**
         * 外部
         */
        String WITHOUT = "without";
    }

    interface transportPlan {
        /**
         * 进行中
         */
        String UNDERWAY = "underway";
        /**
         * 完成
         */
        String OVER = "over";
    }

    /**
     * 船讯网船舶类型
     */
    interface shipType {
        String SHIP_TYPE50 = "50";
        String SHIP_TYPE20[] = {
                "20", "21", "22", "23", "24", "25", "26", "27", "28", "29"
        };
    }

    interface date {
        String SECONDS = "yyyy-MM-dd HH:mm:ss";
        String DAY = "yyyy-MM-dd";
        String HOUR = "yyyy-MM-dd HH";
        String MIN = "yyyy-MM-dd HH:mm";
    }

    interface AIR_BLOWER_PROCESS {
        String HL_MAKE = "B04A02A01";
        String DZ_MAKE = "B04A01A04";
        String TL_MAKE = "B04A01A03";
        String TT_MAKE = "B04A01A02";
        String FJ_MAKE = "B04A01A01";
    }

    interface RECEIVING_STATUS {
        String yd = "yd"; //已读
        String bfyd = "bfyd"; //部分已读
        String wd = "wd"; //未读
    }

    interface READ_STATUS {
        String read = "已读";
        String unread = "未读";
    }

    interface RECTIFICATION_STATUS {
        String pending = "pending";//待发起
        String inProgress = "inProgress";//整改中
        String completed = "completed";//已完成
    }

    interface OVERDUE_STATUS {
        String yq = "yq";//逾期
        String wyq = "wyq";//未逾期
    }

    /**
     * 删除标识 0否 1是
     */
    interface delete_flag {
        int OK = 0;
        int ERROR = 1;
    }

    /**
     * 媒体文件扩展名
     */
    interface mediaType {
        //        .mp4, .avi, .mov, .mkv, .wmv, .flv, .mpeg, .mpg, .m4v, .3gp, .webm, .vob, .ogv
        //        .jpg, .jpeg, .png, .gif, .bmp, .tiff, .tif, .svg, .webp, .ico, .heic, .heif, .raw, .cr2, .nef, .orf, .sr2
        Set<String> VIDEO_EXTENSIONS = Collections.unmodifiableSet(new HashSet<String>() {{
            add(".mp4");
            add(".avi");
            add(".mov");
            add(".mkv");
            add(".wmv");
            add(".flv");
            add(".mpeg");
            add(".mpg");
            add(".m4v");
            add(".3gp");
            add(".webm");
            add(".vob");
            add(".ogv");
        }});
        Set<String> IMAGE_EXTENSIONS = Collections.unmodifiableSet(new HashSet<String>() {{
            add(".jpg");
            add(".jpeg");
            add(".png");
            add(".gif");
            add(".bmp");
            add(".tiff");
            add(".tif");
            add(".svg");
            add(".webp");
            add(".ico");
            add(".heic");
            add(".heif");
            add(".raw");
            add(".cr2");
            add(".nef");
            add(".orf");
            add(".sr2");
        }});
        static boolean isVideoFile(String fileName) {
            return checkExtention(fileName, VIDEO_EXTENSIONS);
        }
        static boolean isImageFile(String fileName) {
            return checkExtention(fileName, IMAGE_EXTENSIONS);
        }
        static boolean checkExtention(String fileName, Set<String> extensions) {
            String lowerCaseFileName = fileName.toLowerCase();
            for (String extension : extensions) {
                if (lowerCaseFileName.endsWith(extension)) {
                    return true;
                }
            }
            return false;
        }
    }
}
