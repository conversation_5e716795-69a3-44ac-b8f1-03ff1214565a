package com.jinghe.breeze.common.utils;

import com.xkcoding.http.util.StringUtil;
import net.sourceforge.pinyin4j.PinyinHelper;

public class PinYinUtil {
    /**
     * 获取一个字符串的拼音首字母
     * 为汉字,字母返回首字母大写,其他返回#
     * 
     * @param input
     * @return
     */
    public static String getFirstCharacter(String input) {
        if (StringUtil.isEmpty(input)) {
            return "#";
        }
        char firstChar = input.charAt(0);
        // 检查是否是汉字
        if (Character.toString(firstChar).matches("[\\u4E00-\\u9FA5]+")) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(firstChar);
            if (pinyinArray != null && pinyinArray.length > 0) {
                // 返回拼音的首字母并转为小写
                return String.valueOf(pinyinArray[0].charAt(0)).toUpperCase();
            } else {
                // 无法转换为拼音
                return "#";
            }
        }
        if (Character.isLetter(firstChar)) {
            // 是字母，返回大写形式
            return String.valueOf(firstChar).toUpperCase();
        }
        // 其他字符
        return "#";
    }
}
