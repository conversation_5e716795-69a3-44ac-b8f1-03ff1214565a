package com.jinghe.breeze.common.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jinghe.breeze.modules.monitor.vo.DataObjectVo;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String[] PARSE_PATTERNS = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * date类型进行格式化输出
     *
     * @param date
     * @param ft   "yyyy-MM-dd HH:mm"
     * @return
     */
    public static String dateFormat(Date date, String ft) {
        SimpleDateFormat formatter = new SimpleDateFormat(ft, Locale.SIMPLIFIED_CHINESE);
        String str = formatter.format(date);
        return str;
    }

    public static Date getDateAfterDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, days);
        return calendar.getTime();
    }

    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 设置时间为当天的零点
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 设置时间为当天的23:59:59和999毫秒
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar.getTime();
    }

    public static String convertToQuarter(String dateString) {
        if (ObjectUtil.isNotEmpty(dateString)) {
            String[] split = dateString.split("-");
            if (ObjectUtil.isNotEmpty(split) && split.length == 2) {
                return split[0] + "年第" + split[1] + "季度";
            } else {
                return dateString;
            }
        }
        return "";
    }

    public static String convertStringToDateFormat(String inputString) {
        // 定义正则表达式模式
        Pattern pattern = Pattern.compile("(\\d{4})年第(\\d{2})季度");
        Matcher matcher = pattern.matcher(inputString);

        if (matcher.find()) {
            // 提取匹配的年份和季度
            String year = matcher.group(1);
            String quarter = matcher.group(2);

            // 将年份和季度格式化为'YYYY-MM'格式
            String formattedDate = year + "-" + quarter;

            return formattedDate;
        } else {
            // 如果输入字符串不匹配预期格式，可以返回一个错误消息或者其他处理方式
            return inputString;
        }
    }

    /**
     * 获取指定日期所在月份开始的时间
     *
     * @param date 指定日期
     * @return
     */
    public static Date getMonthBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);

        return c.getTime();
    }

    /**
     * 获取指定日期所在月份结束的时间
     *
     * @param date 指定日期
     * @return
     */
    public static Date getMonthEnd(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND, 59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 0);
        // 获取本月最后一天的时间戳
        return c.getTime();
    }

    /**
     * 获取指定年份开始的时间
     *
     * @param date 指定日期
     * @return
     */
    public static Date getYearBeginTime(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //设置月份为一月
        c.set(Calendar.MONTH, 0);
        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 获取本月第一天的时间戳
        return c.getTime();
    }

    /**
     * 获取指定年份结束的时间
     *
     * @param date 指定日期
     * @return
     */
    public static Date getYearEndTime(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //设置月份为12
        c.set(Calendar.MONTH, 11);
        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND, 59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月第一天的时间戳
        return c.getTime();
    }

    /**
     * 获取本周的开始时间
     *
     * @return
     */
    @SuppressWarnings("unused")
    public static Date getBeginDayOfWeek(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == 1) {
            dayOfWeek += 7;
        }
        cal.add(Calendar.DATE, 2 - dayOfWeek);
        return getDayStartTime(cal.getTime());
    }


    /**
     * 获取本周的结束时间
     *
     * @return
     */
    public static Date getEndDayOfWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getBeginDayOfWeek(date));
        cal.add(Calendar.DAY_OF_WEEK, 6);
        Date weekEndSta = cal.getTime();
        return getDayEndTime(weekEndSta);
    }

    /**
     * 获取某个日期的开始时间
     *
     * @param d
     * @return
     */
    public static Timestamp getDayStartTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d) {
            calendar.setTime(d);
        }
        calendar.set(calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 获取某个日期的结束时间
     *
     * @param d
     * @return
     */
    public static Timestamp getDayEndTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d) {
            calendar.setTime(d);
        }
        calendar.set(calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 获取两个时间的时间差(返回天)
     *
     * @param datefrom 结束时间
     * @param dateto   开始时间
     * @return
     */
    public static int get2Days(Date datefrom, Date dateto) {
        long day = (datefrom.getTime() - dateto.getTime()) / (24 * 60 * 60 * 1000);
        int days = (int) day;
        return days;
    }

    /**
     * 当前季度的开始
     *
     * @return
     */
    public static Date getQuarterStartTime(Date date) {
        // 获取Calendar实例
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 获取季度开始日期
        int quarter = calendar.get(Calendar.MONTH) / 3 + 1; // 获取季度
        int year = calendar.get(Calendar.YEAR); // 获取年份
        int quarterStartMonth = (quarter - 1) * 3; // 计算季度开始月份
        calendar.set(year, quarterStartMonth, 1); // 设置日期为季度开始日期
        Date dt = calendar.getTime();
        return dt;
    }

    /**
     * 当前季度的结束时间
     *
     * @return
     */
    public static Date getQuarterEndTime(Date date) {
        // 获取Calendar实例
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 获取季度结束日期
        int quarter = calendar.get(Calendar.MONTH) / 3 + 1; // 获取季度
        int year = calendar.get(Calendar.YEAR); // 获取年份
        int quarterStartMonth = (quarter - 1) * 3; // 计算季度开始月份
        calendar.set(year, quarterStartMonth + 3, 1); // 设置日期为季度结束日期
        calendar.add(Calendar.DATE, -1); // 日期减1，获取上一个日期
        Date dt = calendar.getTime();
        return dt;
    }

    /**
     * 获取2个日期间所有的日期 mm-dd
     *
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    public static List<String> getDateToDays(Date startTime, Date endTime) {
        // 返回的日期集合
        List<String> list = new ArrayList<String>();

        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");

        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(startTime);

        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(endTime);
        tempEnd.add(Calendar.DATE, +0);// 日期加1(包含结束)
        while (tempStart.before(tempEnd)) {
            list.add(dateFormat.format(tempStart.getTime()));
            tempStart.add(Calendar.DAY_OF_YEAR, 1);
        }
        return list;
    }

    /**
     * 获取2个日期间所有的小时
     *
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    public static List<Integer> getDateToHour(Date startTime, Date endTime) {
        // 返回的日期集合
        List<Integer> list = new ArrayList<>();

        SimpleDateFormat dateFormat = new SimpleDateFormat("HH");

        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(startTime);

        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(endTime);
        tempEnd.add(Calendar.DATE, +0);// 日期加1(包含结束)

        while (tempStart.before(tempEnd) || tempStart.equals(tempEnd)) {
            String d = dateFormat.format(tempStart.getTime());
            list.add(Integer.valueOf(d));
            tempStart.add(Calendar.HOUR_OF_DAY, 1);
        }
        return list;
    }

    public static boolean isSameYear(Date date1, Date date2) {
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
        String year1 = yearFormat.format(date1);
        String year2 = yearFormat.format(date2);
        return year1.equals(year2);
    }

    public static boolean isSameMonth(Date date1, Date date2) {
        SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
        String month1 = monthFormat.format(date1);
        String month2 = monthFormat.format(date2);
        return month1.equals(month2);
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), PARSE_PATTERNS);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String parseDateToStr(String format, Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    public static String ShowTimeInterval(Date date1, Date date2) {
        long lDate1 = date1.getTime();
        long lDate2 = date2.getTime();
        long diff = (lDate1 < lDate2) ? (lDate2 - lDate1) : (lDate1 - lDate2);
        long hour = diff / (60 * 60 * 1000);
        long min = diff / (60 * 1000) - hour * 60;
        if (hour == 0) {
            if (min == 0) {
                return "";
            }
            return min + "分";
        } else {
            if (min == 0) {
                return hour + "小时";
            }
            return hour + "小时" + min + "分";
        }
    }

    public static List<DataObjectVo> completeAndSortData(int days, List<DataObjectVo> dataList) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 生成日期范围
        List<String> keys = new ArrayList<>();
        for (int i = 0; i < days; i++) {
            keys.add(currentDate.minusDays(i).format(formatter));
        }

        // 将现有数据放入Map以便快速查找
        Map<String, DataObjectVo> dataMap = new HashMap<>();
        String action = null;
        for (DataObjectVo data : dataList) {
            dataMap.put(data.getDay(), data);
            action = data.getAction();
        }

        // 补齐数据
        List<DataObjectVo> completeDataList = new ArrayList<>();
        for (String key : keys) {
            DataObjectVo srcObj = dataMap.get(key);
            if (ObjectUtil.isNotEmpty(srcObj)){
                completeDataList.add(srcObj);
            }else {
                DataObjectVo dataObjectVo = new DataObjectVo();
                dataObjectVo.setDay(key);
                dataObjectVo.setCount("0");
                if (StrUtil.isNotEmpty(action)){
                    dataObjectVo.setAction(action);
                }
                completeDataList.add(dataObjectVo);
            }
        }

        // 按日期升序排序
        completeDataList.sort(Comparator.comparing(DataObjectVo::getDay));

        return completeDataList;
    }
}
