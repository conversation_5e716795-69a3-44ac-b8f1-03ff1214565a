package com.jinghe.breeze.common.utils;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;

import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.system.entity.SysDict;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.jeecg.modules.system.service.ISysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import jodd.util.StringUtil;

@Service
public class SysDictUtil {

    @Autowired
    private ISysDictService sysDictService;

    @Autowired
    private ISysDictItemService sysDictItemService;

    public LinkedHashMap<String, String> getValueKeyDict(String dictCode, String dicriptName) {
        if (StringUtil.isEmpty(dictCode)) {
            throw new JeecgBootException("字典编码不能为空");
        }
        SysDict sysDict = sysDictService.getOne(new LambdaQueryWrapper<SysDict>().eq(SysDict::getDictCode, dictCode),
                false);
        if (sysDict == null) {
            throw new JeecgBootException(String.format("请先配置%s数据字典", dicriptName));
        }
        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByMainId(sysDict.getId());
        sysDictItems.sort(Comparator.comparing(SysDictItem::getSortOrder));
        LinkedHashMap<String, String> retMap = new LinkedHashMap<>();
        sysDictItems.forEach(c -> retMap.put(c.getItemValue(), c.getItemText())); // key 是 B12A02 value 是9#路由
        return retMap;
    }

    public LinkedHashMap<String, String> getKeyValueDict(String dictCode, String dicriptName) {
        LinkedHashMap<String, String> retMap = getValueKeyDict(dictCode, dicriptName);
        LinkedHashMap<String, String> tempMap = new LinkedHashMap<>();
        retMap.forEach((key, value) -> tempMap.put(value, key));
        return tempMap;
    }
}
