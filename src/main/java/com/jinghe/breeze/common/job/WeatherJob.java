package com.jinghe.breeze.common.job;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghe.breeze.common.utils.HttpTools;
import com.jinghe.breeze.modules.sea.entity.*;
import com.jinghe.breeze.modules.sea.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class WeatherJob {
    @Value("${scheduled.is_open}")
    private Boolean isOpen;
    @Autowired
    private SeaEnvironmentHourMapper seaEnHourMapper;

    @Autowired
    private SeaEnvironmentDayMapper seaEnDayMapper;

    @Autowired
    private SeaWeatherLiveMapper seaWeaLiveMapper;

    @Autowired
    private SeaWeatherHourMapper seaWeaHourMapper;

    @Autowired
    private SeaWeatherDayMapper seaWeaDayMapper;
    //海洋环境7天预报
    private String url1 = "https://api.foreocean.com/environment/universal/daily/7d";
    //海洋环境逐小时预报168小时
    private String url2 = "https://api.foreocean.com/environment/universal/hourly/168h";
    //海洋天气实况
    private String url3 = "https://api.foreocean.com/weather/realtime";
    //海洋天气7天预报
    private String url4 = "https://api.foreocean.com/weather/daily/7d";
    //海洋天气逐小时预报168小时
    private String url5 = "https://api.foreocean.com/weather/hourly/168h";

    private String token1 = "56a9ab65b52d98ada600fa8525599728";
    private String token2 = "bf207fa5c002b641a600fa8525599728";
    private String token3 = "ae15849aaa12e304a600fa8525599728";
    private String token4 = "16b6baeb1e15c4aca600fa8525599728";
    private String token5 = "c5d21f82b53257dca600fa8525599728";
    private String location = "108.815007,21.377178";

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH");

    private SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");

    private SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    @Scheduled(cron = "0 1 * * * ?")
    public void autoSynchronizeData() {
        log.info("run weather job1 ***************");
        if (isOpen) {
            getEnvironmentA();
            getEnvironmentB();
            getWeatherA();
            getWeatherB();
            getWeatherC();
        }

    }


    /**
     * 海洋环境7天预报
     */
    private void getEnvironmentA() {
        String resp = HttpTools.doGet(url1 + "?space=" + location + "&version=2.0", token1);
        JSONObject jsonObject = JSONObject.parseObject(resp);
        if (Integer.valueOf(jsonObject.getString("code")) == 1000) {
            log.info(resp);
            Map<Integer, SeaEnvironmentDay> dataMap = new HashMap<>();
            JSONObject data = (JSONObject) jsonObject.get("data");
            String date = data.getString("reportDate");
            Integer firstDay = pureDay(date);
            Integer day2Str = nextDay(firstDay.toString());
            Integer day3Str = nextDay(day2Str.toString());
            Integer day4Str = nextDay(day3Str.toString());
            Integer day5Str = nextDay(day4Str.toString());
            Integer day6Str = nextDay(day5Str.toString());
            Integer day7Str = nextDay(day6Str.toString());
            Integer[] week = {0, firstDay, day2Str, day3Str, day4Str, day5Str, day6Str, day7Str};
            JSONObject portData = (JSONObject) data.get("data");
            JSONArray sst = (JSONArray) portData.get("sst");
            for (Object o : sst) {
                JSONObject item = (JSONObject) o;
                for (int i = 1; i < 8; i++) {
                    String dayStr = "day" + i;
                    JSONObject dayJson = item.getJSONObject(dayStr);
                    SeaEnvironmentDay seaEnvironmentDay = dataMap.get(week[i]);
                    if (seaEnvironmentDay == null) {
                        seaEnvironmentDay = new SeaEnvironmentDay();
                        if (dayJson.getDouble("min") == null) {
                            seaEnvironmentDay.setSstMin(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("min");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setSstMin(new BigDecimal(v2));
                        }
                        if (dayJson.getDouble("max") == null) {
                            seaEnvironmentDay.setSstMax(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("max");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setSstMax(new BigDecimal(v2));
                        }
                        dataMap.put(firstDay, seaEnvironmentDay);
                    } else {
                        if (dayJson.getDouble("min") == null) {
                            seaEnvironmentDay.setSstMin(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("min");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setSstMin(new BigDecimal(v2));
                        }
                        if (dayJson.getDouble("max") == null) {
                            seaEnvironmentDay.setSstMax(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("max");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setSstMax(new BigDecimal(v2));
                        }
                    }
                }
            }

            JSONObject seaWater = portData.getJSONObject("seawater");
            JSONArray waterSpeed = seaWater.getJSONArray("waterSpeed");
            for (Object o : waterSpeed) {
                JSONObject item = (JSONObject) o;
                for (int i = 1; i < 8; i++) {
                    String dayStr = "day" + i;
                    JSONObject dayJson = item.getJSONObject(dayStr);
                    SeaEnvironmentDay seaEnvironmentDay = dataMap.get(week[i]);
                    if (seaEnvironmentDay == null) {
                        seaEnvironmentDay = new SeaEnvironmentDay();
                        if (dayJson.getDouble("min") == null) {
                            seaEnvironmentDay.setWaterSpeedMin(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("min");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWaterSpeedMin(new BigDecimal(v2));
                        }
                        if (dayJson.getDouble("max") == null) {
                            seaEnvironmentDay.setWaterSpeedMax(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("max");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWaterSpeedMax(new BigDecimal(v2));
                        }
                        dataMap.put(firstDay, seaEnvironmentDay);
                    } else {
                        if (dayJson.getDouble("min") == null) {
                            seaEnvironmentDay.setWaterSpeedMin(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("min");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWaterSpeedMin(new BigDecimal(v2));
                        }
                        if (dayJson.getDouble("max") == null) {
                            seaEnvironmentDay.setWaterSpeedMax(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("max");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWaterSpeedMax(new BigDecimal(v2));
                        }
                    }
                }
            }

            JSONObject wave = portData.getJSONObject("wave");
            JSONArray waveHeight = wave.getJSONArray("waveHeight");
            for (Object o : waveHeight) {
                JSONObject item = (JSONObject) o;
                for (int i = 1; i < 8; i++) {
                    String dayStr = "day" + i;
                    JSONObject dayJson = item.getJSONObject(dayStr);
                    SeaEnvironmentDay seaEnvironmentDay = dataMap.get(week[i]);
                    if (seaEnvironmentDay == null) {
                        seaEnvironmentDay = new SeaEnvironmentDay();
                        if (dayJson.getDouble("min") == null) {
                            seaEnvironmentDay.setWaveHeightMin(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("min");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWaveHeightMin(new BigDecimal(v2));
                        }
                        if (dayJson.getDouble("max") == null) {
                            seaEnvironmentDay.setWaveHeightMax(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("max");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWaveHeightMax(new BigDecimal(v2));
                        }
                        dataMap.put(firstDay, seaEnvironmentDay);
                    } else {
                        if (dayJson.getDouble("min") == null) {
                            seaEnvironmentDay.setWaveHeightMin(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("min");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWaveHeightMin(new BigDecimal(v2));
                        }
                        if (dayJson.getDouble("max") == null) {
                            seaEnvironmentDay.setWaveHeightMax(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("max");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWaveHeightMax(new BigDecimal(v2));
                        }
                    }
                }
            }

            JSONObject wind = portData.getJSONObject("wind");
            JSONArray windSpeed = wind.getJSONArray("windSpeed");
            for (Object o : windSpeed) {
                JSONObject item = (JSONObject) o;
                for (int i = 1; i < 8; i++) {
                    String dayStr = "day" + i;
                    JSONObject dayJson = item.getJSONObject(dayStr);
                    SeaEnvironmentDay seaEnvironmentDay = dataMap.get(week[i]);
                    if (seaEnvironmentDay == null) {
                        seaEnvironmentDay = new SeaEnvironmentDay();
                        if (dayJson.getDouble("min") == null) {
                            seaEnvironmentDay.setWindSpeedMin(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("min");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWindSpeedMin(new BigDecimal(v2));
                        }
                        if (dayJson.getDouble("max") == null) {
                            seaEnvironmentDay.setWindSpeedMax(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("max");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWindSpeedMax(new BigDecimal(v2));
                        }
                        dataMap.put(firstDay, seaEnvironmentDay);
                    } else {
                        if (dayJson.getDouble("min") == null) {
                            seaEnvironmentDay.setWindSpeedMin(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("min");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWindSpeedMin(new BigDecimal(v2));
                        }
                        if (dayJson.getDouble("max") == null) {
                            seaEnvironmentDay.setWindSpeedMax(new BigDecimal(0));
                        } else {
                            double v = dayJson.getDouble("max");
                            double v2 = Math.round(v * 10.0) / 10.0;
                            seaEnvironmentDay.setWindSpeedMax(new BigDecimal(v2));
                        }
                    }
                }
            }

            for (Map.Entry<Integer, SeaEnvironmentDay> entry : dataMap.entrySet()) {
                SeaEnvironmentDay dayValue = entry.getValue();
                Integer key = entry.getKey();
                LambdaQueryWrapper<SeaEnvironmentDay> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SeaEnvironmentDay::getDay, key);
                SeaEnvironmentDay dbItem = seaEnDayMapper.selectOne(wrapper);
                dayValue.setDay(key);
                if (dbItem == null) {
                    seaEnDayMapper.insert(dayValue);
                } else {
                    dayValue.setId(dbItem.getId());
                    seaEnDayMapper.updateById(dayValue);
                }
            }
        } else {
            log.error(resp);
        }
    }

    /**
     * 海洋环境逐小时预报168小时
     */
    private void getEnvironmentB() {
        String resp = HttpTools.doGet(url2 + "?space=" + location + "&version=2.0", token2);
        JSONObject jsonObject = JSONObject.parseObject(resp);
        if (Integer.valueOf(jsonObject.getString("code")) == 1000) {
            Map<Integer, SeaEnvironmentHour> dataMap = new HashMap<>();
            JSONObject data = (JSONObject) jsonObject.get("data");
            JSONObject portData = (JSONObject) data.get("data");
            JSONArray sst = (JSONArray) portData.get("sst");
            for (Object o : sst) {
                JSONObject item = (JSONObject) o;
                Integer hour = this.pureHour(item.getString("datatime"));
                SeaEnvironmentHour seaEnvironmentHour = dataMap.get(hour);
                if (seaEnvironmentHour == null) {
                    seaEnvironmentHour = new SeaEnvironmentHour();
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setSst(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setSst(new BigDecimal(v2));
                    }
                    dataMap.put(hour, seaEnvironmentHour);
                } else {
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setSst(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setSst(new BigDecimal(v2));
                    }
                }
            }

            JSONObject seawater = (JSONObject) portData.get("seawater");
            JSONArray waterSpeed = (JSONArray) seawater.get("waterSpeed");
            for (Object o : waterSpeed) {
                JSONObject item = (JSONObject) o;
                Integer hour = this.pureHour(item.getString("datatime"));
                SeaEnvironmentHour seaEnvironmentHour = dataMap.get(hour);
                if (seaEnvironmentHour == null) {
                    seaEnvironmentHour = new SeaEnvironmentHour();
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWaterSpeed(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWaterSpeed(new BigDecimal(v2));
                    }
                    dataMap.put(hour, seaEnvironmentHour);
                } else {
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWaterSpeed(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWaterSpeed(new BigDecimal(v2));
                    }
                }
            }
            JSONArray waterDirection = (JSONArray) seawater.get("waterDirection");
            for (Object o : waterDirection) {
                JSONObject item = (JSONObject) o;
                Integer hour = this.pureHour(item.getString("datatime"));
                SeaEnvironmentHour seaEnvironmentHour = dataMap.get(hour);
                if (seaEnvironmentHour == null) {
                    seaEnvironmentHour = new SeaEnvironmentHour();
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWaterDirection(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWaterDirection(new BigDecimal(v2));
                    }
                    dataMap.put(hour, seaEnvironmentHour);
                } else {
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWaterDirection(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWaterDirection(new BigDecimal(v2));
                    }
                }
            }

            JSONObject wave = (JSONObject) portData.get("wave");
            JSONArray waveHeight = (JSONArray) wave.get("waveHeight");
            for (Object o : waveHeight) {
                JSONObject item = (JSONObject) o;
                Integer hour = this.pureHour(item.getString("datatime"));
                SeaEnvironmentHour seaEnvironmentHour = dataMap.get(hour);
                if (seaEnvironmentHour == null) {
                    seaEnvironmentHour = new SeaEnvironmentHour();
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWaveHeight(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWaveHeight(new BigDecimal(v2));
                    }
                    dataMap.put(hour, seaEnvironmentHour);
                } else {
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWaveHeight(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWaveHeight(new BigDecimal(v2));
                    }
                }
            }
            JSONArray waveDirection = (JSONArray) wave.get("waveDirection");
            for (Object o : waveDirection) {
                JSONObject item = (JSONObject) o;
                Integer hour = this.pureHour(item.getString("datatime"));
                SeaEnvironmentHour seaEnvironmentHour = dataMap.get(hour);
                if (seaEnvironmentHour == null) {
                    seaEnvironmentHour = new SeaEnvironmentHour();
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWaveDirection(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWaveDirection(new BigDecimal(v2));
                    }
                    dataMap.put(hour, seaEnvironmentHour);
                } else {
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWaveDirection(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWaveDirection(new BigDecimal(v2));
                    }
                }
            }
            JSONArray wavePeriod = (JSONArray) wave.get("wavePeriod");
            for (Object o : wavePeriod) {
                JSONObject item = (JSONObject) o;
                Integer hour = this.pureHour(item.getString("datatime"));
                SeaEnvironmentHour seaEnvironmentHour = dataMap.get(hour);
                if (seaEnvironmentHour == null) {
                    seaEnvironmentHour = new SeaEnvironmentHour();
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWavePeriod(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWavePeriod(new BigDecimal(v2));
                    }
                    dataMap.put(hour, seaEnvironmentHour);
                } else {
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWavePeriod(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWavePeriod(new BigDecimal(v2));
                    }
                }
            }

            JSONObject wind = (JSONObject) portData.get("wind");
            JSONArray windSpeed = (JSONArray) wind.get("windSpeed");
            for (Object o : windSpeed) {
                JSONObject item = (JSONObject) o;
                Integer hour = this.pureHour(item.getString("datatime"));
                SeaEnvironmentHour seaEnvironmentHour = dataMap.get(hour);
                if (seaEnvironmentHour == null) {
                    seaEnvironmentHour = new SeaEnvironmentHour();
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWindSpeed(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWindSpeed(new BigDecimal(v2));
                    }
                    dataMap.put(hour, seaEnvironmentHour);
                } else {
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWindSpeed(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWindSpeed(new BigDecimal(v2));
                    }
                }
            }
            JSONArray windDirections = (JSONArray) wind.get("windDirection");
            for (Object o : windDirections) {
                JSONObject item = (JSONObject) o;
                Integer hour = this.pureHour(item.getString("datatime"));
                SeaEnvironmentHour seaEnvironmentHour = dataMap.get(hour);
                if (seaEnvironmentHour == null) {
                    seaEnvironmentHour = new SeaEnvironmentHour();
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWindDirection(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWindDirection(new BigDecimal(v2));
                    }
                    dataMap.put(hour, seaEnvironmentHour);
                } else {
                    if (item.getDouble("value") == null) {
                        seaEnvironmentHour.setWindDirection(new BigDecimal(0));
                    } else {
                        double v = item.getDouble("value");
                        double v2 = Math.round(v * 10.0) / 10.0;
                        seaEnvironmentHour.setWindDirection(new BigDecimal(v2));
                    }
                }
            }
            for (Map.Entry<Integer, SeaEnvironmentHour> entry : dataMap.entrySet()) {
                SeaEnvironmentHour hourValue = entry.getValue();
                Integer key = entry.getKey();
                LambdaQueryWrapper<SeaEnvironmentHour> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SeaEnvironmentHour::getHour, key);
                wrapper.last("limit 1");
                SeaEnvironmentHour dbItem = seaEnHourMapper.selectOne(wrapper);
                hourValue.setHour(key);
                if (dbItem == null) {
                    seaEnHourMapper.insert(hourValue);
                } else {
                    hourValue.setId(dbItem.getId());
                    seaEnHourMapper.updateById(hourValue);
                }
            }
        } else {
            log.error(resp);
        }
    }


    /**
     * 实时天气数据
     */
    private void getWeatherA() {
        String resp = HttpTools.doGet(url3 + "?location=" + location + "&version=2.0", token3);
        JSONObject jsonObject = JSONObject.parseObject(resp);
        if (Integer.valueOf(jsonObject.getString("code")) == 1000) {
            log.info(resp);
            SeaWeatherLive seaWeatherLive = new SeaWeatherLive();
            JSONObject data = (JSONObject) jsonObject.get("data");
//            Integer hour = pureHour(data.getString("dateTime"));
            Integer hour = Integer.parseInt(sdf.format(new Date()));
            seaWeatherLive.setHour(hour);
            seaWeatherLive.setCityName(data.getString("cityName"));
            Date dateTime = null;
            if (data.getString("dateTime") != null) {
                try {
                    dateTime = sdf3.parse(data.getString("dateTime"));
                    seaWeatherLive.setDateTime(dateTime);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
            seaWeatherLive.setPod(data.getString("pod"));
            //
            BigDecimal visibility = new BigDecimal(data.getString("visibility"));
            seaWeatherLive.setVisibility(visibility.setScale(1, RoundingMode.HALF_UP));
            seaWeatherLive.setWeatherDescription(data.getString("weatherDescription"));
            seaWeatherLive.setWeatherCode(data.getInteger("weatherCode"));
            seaWeatherLive.setClouds(data.getInteger("clouds"));
            seaWeatherLive.setPressure(new BigDecimal(data.getString("pressure")));
            //
            BigDecimal precipitation = new BigDecimal(data.getString("precipitation"));
            seaWeatherLive.setPrecipitation(precipitation.setScale(2, RoundingMode.HALF_UP));
            seaWeatherLive.setSnow(new BigDecimal(data.getString("snow")));
            seaWeatherLive.setTemperature(new BigDecimal(data.getString("temperature")));
            seaWeatherLive.setAppTemp(new BigDecimal(data.getString("appTemp")));
            //
            BigDecimal ultravioletRays = new BigDecimal(data.getString("ultravioletRays"));
            seaWeatherLive.setUltravioletRays(ultravioletRays.setScale(0, RoundingMode.HALF_UP));
            seaWeatherLive.setRelaHumidity(new BigDecimal(data.getString("rHumidity")));
            seaWeatherLive.setWindDirection(new BigDecimal(data.getString("windDirection")));
            //
            BigDecimal windSpeed = new BigDecimal(data.getString("windSpeed"));
            seaWeatherLive.setWindSpeed(windSpeed.setScale(1, RoundingMode.HALF_UP));
            seaWeatherLive.setWindMark(data.getString("windMark"));

            LambdaQueryWrapper<SeaEnvironmentHour> waveWrapper = new LambdaQueryWrapper<>();
            waveWrapper.eq(SeaEnvironmentHour::getHour, hour);
            waveWrapper.last("limit 1");
            SeaEnvironmentHour waveData = seaEnHourMapper.selectOne(waveWrapper);
            if (waveData != null) {
                seaWeatherLive.setWaveHeight(waveData.getWaveHeight());
            }

            LambdaQueryWrapper<SeaWeatherLive> wrapper = new LambdaQueryWrapper<>();
            wrapper.orderByDesc(SeaWeatherLive::getCreateTime);
            wrapper.last("limit 1");
            wrapper.eq(SeaWeatherLive::getHour, hour);
            SeaWeatherLive dbItem = seaWeaLiveMapper.selectOne(wrapper);
            if (dbItem == null) {
                seaWeaLiveMapper.insert(seaWeatherLive);
            } else {
                seaWeatherLive.setId(dbItem.getId());
                seaWeaLiveMapper.updateById(seaWeatherLive);
            }
        } else {
            log.error(resp);
        }
    }


    /**
     * 海洋天气7天预报
     */
    private void getWeatherB() {
        String resp = HttpTools.doGet(url4 + "?location=" + location + "&version=2.0", token4);
        JSONObject jsonObject = JSONObject.parseObject(resp);
        if (Integer.valueOf(jsonObject.getString("code")) == 1000) {
            JSONArray data = jsonObject.getJSONArray("data");
            List<SeaWeatherDay> dataList = new ArrayList<>();
            for (Object o : data) {
                JSONObject item = (JSONObject) o;
                SeaWeatherDay seaWeatherDay = new SeaWeatherDay();
                Integer day = pureDay(item.getString("dateTime"));
                seaWeatherDay.setDay(day);
                seaWeatherDay.setCityName(item.getString("cityName"));
                BigDecimal visibility = new BigDecimal(item.getString("visibility"));
                seaWeatherDay.setVisibility(visibility.setScale(1, RoundingMode.HALF_UP));
                seaWeatherDay.setWeatherDescription(item.getString("weatherDescription"));
                seaWeatherDay.setWeatherCode(item.getInteger("weatherCode"));
                seaWeatherDay.setClouds(item.getInteger("clouds"));
                seaWeatherDay.setPressure(new BigDecimal(item.getString("pressure")));
                seaWeatherDay.setSeaPressure(new BigDecimal(item.getString("seaPressure")));
                BigDecimal precipitation = new BigDecimal(item.getString("precipitation"));
                seaWeatherDay.setPrecipitation(precipitation.setScale(1, RoundingMode.HALF_UP));
                seaWeatherDay.setProbability(new BigDecimal(item.getString("probability")));
                seaWeatherDay.setSnow(new BigDecimal(item.getString("snow")));
                seaWeatherDay.setSnowDepth(new BigDecimal(item.getString("snowDepth")));
                seaWeatherDay.setMinTemperature(new BigDecimal(item.getString("minTemperature")));
                seaWeatherDay.setMaxTemperature(new BigDecimal(item.getString("maxTemperature")));
                seaWeatherDay.setLowTemperature(new BigDecimal(item.getString("lowTemperature")));
                seaWeatherDay.setHighTemperature(new BigDecimal(item.getString("highTemperature")));
                seaWeatherDay.setAverageTemperature(new BigDecimal(item.getString("averageTemperature")));
                seaWeatherDay.setMinAppTemp(new BigDecimal(item.getString("minAppTemp")));
                seaWeatherDay.setMaxAppTemp(new BigDecimal(item.getString("maxAppTemp")));
                BigDecimal ultravioletRays = new BigDecimal(item.getString("ultravioletRays"));
                seaWeatherDay.setUltravioletRays(ultravioletRays.setScale(0, RoundingMode.HALF_UP));
                seaWeatherDay.setRelaHumidity(new BigDecimal(item.getString("rHumidity")));
                seaWeatherDay.setWindDirection(new BigDecimal(item.getString("windDirection")));
                BigDecimal windSpeed = new BigDecimal(item.getString("windSpeed"));
                seaWeatherDay.setWindSpeed(windSpeed.setScale(1, RoundingMode.HALF_UP));
                seaWeatherDay.setWindMark(item.getString("windMark"));

                dataList.add(seaWeatherDay);
            }
            for (SeaWeatherDay dayItem : dataList) {
                LambdaQueryWrapper<SeaWeatherDay> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SeaWeatherDay::getDay, dayItem.getDay());
                SeaWeatherDay dbItem = seaWeaDayMapper.selectOne(wrapper);
                if (dbItem == null) {
                    seaWeaDayMapper.insert(dayItem);
                } else {
                    dayItem.setId(dbItem.getId());
                    seaWeaDayMapper.updateById(dayItem);
                }
            }
        } else {
            log.error(resp);
        }
    }

    /**
     * 海洋天气逐小时预报168小时
     */
    private void getWeatherC() {
        String resp = HttpTools.doGet(url5 + "?location=" + location + "&version=2.0", token5);
        log.info(resp);
        JSONObject jsonObject = JSONObject.parseObject(resp);
        if (Integer.valueOf(jsonObject.getString("code")) == 1000) {
            JSONArray data = jsonObject.getJSONArray("data");
            List<SeaWeatherHour> dataList = new ArrayList<>();
            for (Object o : data) {
                JSONObject item = (JSONObject) o;
                SeaWeatherHour seaWeatherLive = new SeaWeatherHour();
                Integer day = pureHour(item.getString("dateTime"));
                seaWeatherLive.setHour(day);
                seaWeatherLive.setPod(item.getString("pod"));
                seaWeatherLive.setCityName(item.getString("cityName"));
                //
                BigDecimal visibility = new BigDecimal(item.getString("visibility"));
                seaWeatherLive.setVisibility(visibility.setScale(1, RoundingMode.HALF_UP));
                seaWeatherLive.setWeatherDescription(item.getString("weatherDescription"));
                seaWeatherLive.setWeatherCode(item.getInteger("weatherCode"));
                seaWeatherLive.setClouds(item.getInteger("clouds"));
                seaWeatherLive.setPressure(new BigDecimal(item.getString("pressure")));
                seaWeatherLive.setSeaPressure(new BigDecimal(item.getString("seaPressure")));

                BigDecimal precipitation = new BigDecimal(item.getString("precipitation"));
                seaWeatherLive.setPrecipitation(precipitation.setScale(2, RoundingMode.HALF_UP));
                seaWeatherLive.setProbability(new BigDecimal(item.getString("probability")));
                seaWeatherLive.setSnow(new BigDecimal(item.getString("snow")));
                seaWeatherLive.setSnowDepth(new BigDecimal(item.getString("snowDepth")));
                seaWeatherLive.setTemperature(new BigDecimal(item.getString("temperature")));
                seaWeatherLive.setAppTemp(new BigDecimal(item.getString("appTemp")));
                BigDecimal ultravioletRays = new BigDecimal(item.getString("ultravioletRays"));
                seaWeatherLive.setUltravioletRays(ultravioletRays.setScale(0, RoundingMode.HALF_UP));
                seaWeatherLive.setRelaHumidity(new BigDecimal(item.getString("rHumidity")));
                seaWeatherLive.setWindDirection(new BigDecimal(item.getString("windDirection")));
                BigDecimal windSpeed = new BigDecimal(item.getString("windSpeed"));
                seaWeatherLive.setUltravioletRays(ultravioletRays.setScale(0, RoundingMode.HALF_UP));
                seaWeatherLive.setWindSpeed(windSpeed.setScale(1, RoundingMode.HALF_UP));
                seaWeatherLive.setWindMark(item.getString("windMark"));

                dataList.add(seaWeatherLive);
            }
            for (SeaWeatherHour item : dataList) {
                LambdaQueryWrapper<SeaWeatherHour> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SeaWeatherHour::getHour, item.getHour());
                SeaWeatherHour dbItem = seaWeaHourMapper.selectOne(wrapper);
                if (dbItem == null) {
                    seaWeaHourMapper.insert(item);
                } else {
                    item.setId(dbItem.getId());
                    seaWeaHourMapper.updateById(item);
                }
            }
        } else {
            log.error(resp);
        }
    }

    private Integer pureHour(String param) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(param);
        StringBuilder numbers = new StringBuilder();
        while (matcher.find()) {
            numbers.append(matcher.group());
        }
        String timeStr = numbers.toString();
        if (timeStr.length() > 10) {
            return Integer.parseInt(timeStr.substring(0, 10));
        } else if (timeStr.length() < 10) {
            return 0;
        } else {
            return Integer.parseInt(timeStr);
        }
    }

    private Integer pureDay(String param) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(param);
        StringBuilder numbers = new StringBuilder();
        while (matcher.find()) {
            numbers.append(matcher.group());
        }
        String timeStr = numbers.toString();
        if (timeStr.length() > 8) {
            return Integer.parseInt(timeStr.substring(0, 8));
        } else if (timeStr.length() < 8) {
            return 0;
        } else {
            return Integer.parseInt(timeStr);
        }
    }

    private Integer nextDay(String param) {
        try {
            Date parse = sdf2.parse(param);
            return Integer.parseInt(sdf2.format(new Date(parse.getTime() + 86400000)));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }


    public void getSeaWeather(String lon, String lat, SeaWeatherVo vo) {
        //海洋天气逐小时
        String resp = HttpTools.doGet(url5 + "?location=" + lon + "," + lat + "&version=2.0", token5);
        log.info(resp);
        JSONObject jsonObject = JSONObject.parseObject(resp);
        if (jsonObject.getInteger("code").equals(1000)) {
            JSONArray data = jsonObject.getJSONArray("data");
            List<SeaWeatherHour> dataList = new ArrayList<>();
            JSONObject item = (JSONObject) data.get(0);
            vo.setTemperature(new BigDecimal(item.getString("temperature")));
            vo.setVisibility(new BigDecimal(item.getString("visibility")));
            vo.setRelaHumidity(new BigDecimal(item.getString("rHumidity")));
            vo.setWindSpeed(new BigDecimal(item.getDouble("windSpeed")));
            vo.setWindMark(item.getString("windMark"));
            vo.setPrecipitation(new BigDecimal(item.getString("precipitation")));
        } else {
            log.error(resp);
        }
        String resp2 = HttpTools.doGet(url2 + "?space=" + lon + "," + lat + "&version=2.0", token2);
        JSONObject jsonObject2 = JSONObject.parseObject(resp2);
        if (jsonObject.getInteger("code").equals(1000)) {
            Map<Integer, SeaEnvironmentHour> dataMap = new HashMap<>();
            JSONObject data = (JSONObject) jsonObject2.get("data");
            JSONObject portData = (JSONObject) data.get("data");

            JSONObject wave = (JSONObject) portData.get("wave");
            JSONArray waveHeight = (JSONArray) wave.get("waveHeight");
            JSONObject waveHeightItem = (JSONObject) waveHeight.get(0);
            if (waveHeightItem.getDouble("value") == null) {
                vo.setWaveHeight(new BigDecimal(0));
            } else {
                double v = waveHeightItem.getDouble("value");
                double v2 = Math.round(v * 10.0) / 10.0;
                vo.setWaveHeight(new BigDecimal(v2));
            }

            JSONArray waveDirection = (JSONArray) wave.get("waveDirection");
            JSONObject waveDirectionItem = (JSONObject) waveDirection.get(0);
            if (waveDirectionItem.getDouble("value") == null) {
                vo.setWaveDirection(new BigDecimal(0));
            } else {
                vo.setWaveDirection(new BigDecimal(waveDirectionItem.getDouble("value")));
            }

            JSONArray wavePeriod = (JSONArray) wave.get("wavePeriod");
            JSONObject wavePeriodItem = (JSONObject) wavePeriod.get(0);
            if (wavePeriodItem.getDouble("value") == null) {
                vo.setWavePeriod(new BigDecimal(0));
            } else {
                double v = wavePeriodItem.getDouble("value");
                double v2 = Math.round(v * 10.0) / 10.0;
                vo.setWavePeriod(new BigDecimal(v2));
            }

        } else {
            log.error(resp2);
        }

    }

}
