package com.jinghe.breeze.common.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.constants.enums.ProjectFensEnum;
import com.jinghe.breeze.common.redis.utils.RedisTool;
import com.jinghe.breeze.common.utils.MapPoint;
import com.jinghe.breeze.common.utils.MapWktUtil;
import com.jinghe.breeze.modules.project.entity.ProjectInfo;
import com.jinghe.breeze.modules.project.service.IProjectInfoService;
import com.jinghe.breeze.modules.ship.entity.ShipAlarm;
import com.jinghe.breeze.modules.ship.entity.ShipAlarmDetail;
import com.jinghe.breeze.modules.ship.entity.ShipInfo;
import com.jinghe.breeze.modules.ship.service.IShipAlarmListService;
import com.jinghe.breeze.modules.ship.service.IShipAlarmService;

import com.jinghe.breeze.modules.project.entity.ProjectFence;
import com.jinghe.breeze.modules.project.mapper.ProjectFenceMapper;
import com.jinghe.breeze.modules.ship.service.IShipApiService;
import com.jinghe.breeze.modules.ship.service.IShipInfoService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.modules.system.mapper.SysDictMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class JobShipAlarm {

    @Resource
    private RedisTool redisTool;
    @Resource
    private ProjectFenceMapper fenceMapper;
    @Resource
    private IShipApiService iShipApiService;
    @Resource
    private IShipInfoService iShipInfoService;
    @Resource
    private IShipAlarmService iShipAlarmService;
    @Resource
    private IShipAlarmListService iShipAlarmListService;
    @Resource
    private SysDictMapper sysDictMapper;
    @Resource
    private IProjectInfoService iProjectInfoService;

//    @Scheduled(cron = "0 0/1 * * * ?")
    public void shipAlarm() {
        try {
            if (redisTool.hasKey(Common.TaskFlag.ALARM_TASK_KEY)) {
                //没五分钟执行一次，设置redis四分钟过期
                redisTool.expire(Common.TaskFlag.ALARM_TASK_KEY, 1000L * 60 * 4);
            } else {
                redisTool.set(Common.TaskFlag.ALARM_TASK_KEY, 1, 1000L * 60 * 4);
            }
            LambdaQueryWrapper<ProjectFence> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectFence::getFenceType, ProjectFensEnum.YJ.getCode());
            List<ProjectFence> projectFences = fenceMapper.selectList(queryWrapper);
            if (projectFences.size() == 0) {
                return;
            }//oConvertUtils
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String parse = sdf.format(date);
            List<DictModel> ship_type = sysDictMapper.queryDictItemsByCode("ship_type");
            if (ship_type.size() == 0) {
                return;
            }
            Map<String, List<DictModel>> collect = ship_type.stream().collect(Collectors.groupingBy(
                    sorce -> sorce.getValue()
            ));

            LambdaQueryWrapper<ShipInfo> shipInfo = new LambdaQueryWrapper<>();
//            shipInfo.eq(ShipInfo::getMmsi, mmsi);
            shipInfo.eq(ShipInfo::getDelFlag, Common.delete_flag.OK);
            List<ShipInfo> list1 = iShipInfoService.list(shipInfo);
            List<String> mmsiList = list1.stream().map(x -> x.getMmsi()).collect(Collectors.toList());
            LambdaQueryWrapper<ProjectInfo> projectInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            projectInfoLambdaQueryWrapper.eq(ProjectInfo::getDelFlag, Common.delete_flag.OK);
            List<ProjectInfo> projectInfos = iProjectInfoService.list(projectInfoLambdaQueryWrapper);
            if (projectInfos.size() == 0) {
                return;
            }
            for (ProjectInfo projectInfo : projectInfos) {
                for (ProjectFence projectFence : projectFences) {
                    String projectId = projectFence.getProjectId();
//                    String points = points(projectFence.getFenceRadius());
                    if (StringUtils.isEmpty(projectInfo.getSiteRange())){
                        continue;
                    }
                    String projectPoints = MapWktUtil.points(projectInfo.getSiteRange());
//                    Result<?> result = iShipApiService.GetAreaShip(points);
                    Result<?> result = iShipApiService.GetAreaShip(projectPoints);
                    if (result.isSuccess()) {
                        List data = (List) result.getResult();
                        if (StringUtils.isEmpty(data) || data.size() == 0) {
                            continue;
                        }
                        for (Object datum : data) {
                            JSONObject jsonObject = JSON.parseObject(datum.toString());
                            List<DictModel> shiptype = collect.get(jsonObject.getString("shiptype"));
                            String shipTypeName = "";
                            if (StringUtils.isEmpty(shiptype)) {
                                shipTypeName = "未知船舶";
                            } else {
                                shipTypeName = shiptype.get(0).getText();
                            }
                            String mmsi = jsonObject.getString("mmsi");
                            LambdaQueryWrapper<ShipAlarm> queryWrapper1 = new LambdaQueryWrapper<>();
                            queryWrapper1.eq(ShipAlarm::getAlarmDay, parse);
                            queryWrapper1.eq(ShipAlarm::getMmsi, mmsi);
                            List<ShipAlarm> list = iShipAlarmService.list(queryWrapper1);
                            ShipAlarm shipAlarm = new ShipAlarm();
                            boolean pointInPolygon = MapWktUtil.isPointInPolygon(new MapPoint(Double.parseDouble(jsonObject.getString("lon")) / 1000000,
                                            Double.parseDouble(jsonObject.getString("lat")) / 1000000),
                                    projectFence.getFenceRadius());
                            int isIn = 0;
                            if (pointInPolygon) {
                                isIn = 1;
                            }
                            if (list.size() == 0) {
                                shipAlarm.setProjectId(projectId);
                                shipAlarm.setAlarmDay(parse);
                                if (!StringUtils.isEmpty(jsonObject.getString("cnname"))) {
                                    shipAlarm.setShipName(jsonObject.getString("cnname"));
                                } else {
                                    shipAlarm.setShipName(jsonObject.getString("name"));
                                }
                                shipAlarm.setMmsi(jsonObject.getString("mmsi"));

                                if (mmsiList.contains(mmsi)) {
                                    shipAlarm.setBelong(Common.belong.INTERIOR);
                                } else {
                                    shipAlarm.setBelong(Common.belong.WITHOUT);
                                }

                                shipAlarm.setShipTypeName(shipTypeName);
                                shipAlarm.setShipType(jsonObject.getString("shiptype"));
                                shipAlarm.setEndTime(date);
                                shipAlarm.setIsIn(isIn);
                                shipAlarm.setFirstTime(date);
                                shipAlarm.setDest(jsonObject.getString("dest"));
                                shipAlarm.setDestStd(jsonObject.getString("dest_std"));
                                shipAlarm.setDraught(jsonObject.getInteger("draught"));
                                shipAlarm.setEta(jsonObject.getString("eta"));
                                shipAlarm.setEtaStd(jsonObject.getString("eta_std"));
                                shipAlarm.setShipLeft(jsonObject.getInteger("left"));
                                shipAlarm.setLength(jsonObject.getInteger("length"));
                                shipAlarm.setTrail(jsonObject.getInteger("trail"));
                                shipAlarm.setWidth(jsonObject.getInteger("width"));

                                iShipAlarmService.save(shipAlarm);
                            } else {
                                shipAlarm.setEndTime(date);
                                if (!StringUtils.isEmpty(list.get(0).getIsIn()) && list.get(0).getIsIn() != 1) {
                                    shipAlarm.setIsIn(isIn);
                                }
                                LambdaUpdateWrapper<ShipAlarm> updateWrapper = new LambdaUpdateWrapper<>();
                                updateWrapper.eq(ShipAlarm::getId, list.get(0).getId());
                                iShipAlarmService.update(shipAlarm, updateWrapper);
                            }
                            insertList(jsonObject, parse, date, mmsi, projectId, shipTypeName, isIn);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void insertList(JSONObject jsonObject, String parse, Date date, String mmsi, String projectId, String shipTypeName, int isIn) {
        ShipAlarmDetail shipAlarmDetail = new ShipAlarmDetail();
        shipAlarmDetail.setAlarmDate(date);
        shipAlarmDetail.setAlarmDay(parse);
        shipAlarmDetail.setIsIn(isIn);
        shipAlarmDetail.setShipTypeName(shipTypeName);
        shipAlarmDetail.setShipType(jsonObject.getString("shiptype"));
        shipAlarmDetail.setCallSign(jsonObject.getString("callsign"));
        shipAlarmDetail.setCog(jsonObject.getInteger("cog"));
        shipAlarmDetail.setHdg(jsonObject.getInteger("hdg"));
        shipAlarmDetail.setLat(new BigDecimal(jsonObject.getString("lat")).divide(new BigDecimal("1000000"), RoundingMode.HALF_UP));
        shipAlarmDetail.setLon(new BigDecimal(jsonObject.getString("lon")).divide(new BigDecimal("1000000"),RoundingMode.HALF_UP));
        shipAlarmDetail.setMmsi(mmsi);
        shipAlarmDetail.setProjectId(projectId);
        shipAlarmDetail.setShipName(jsonObject.getString("name"));
        shipAlarmDetail.setSog(jsonObject.getInteger("sog"));
        shipAlarmDetail.setImo(jsonObject.getString("imo"));
        shipAlarmDetail.setRot(jsonObject.getInteger("rot"));
        shipAlarmDetail.setNavistat(jsonObject.getString("navistat"));
        shipAlarmDetail.setLastTime(jsonObject.getInteger("lastTime"));
        iShipAlarmListService.save(shipAlarmDetail);
    }


}
