package com.jinghe.breeze.common.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jinghe.breeze.common.constants.Common;
import com.jinghe.breeze.common.utils.WeatherUtil;
import com.jinghe.breeze.modules.project.entity.ProjectInfo;
import com.jinghe.breeze.modules.project.mapper.ProjectInfoMapper;
import com.jinghe.breeze.modules.sea.entity.ExtremeWeather;
import com.jinghe.breeze.modules.sea.service.IExtremeWeatherService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.entity.SysDict;
import org.jeecg.modules.system.service.ISysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ExtremeWeatherJob {
    @Value("${scheduled.is_open}")
    private Boolean isOpen;
    @Value("${weather.extreme-url}")
    private String url;
    @Value("${weather.extreme-token}")
    private String token;
    @Autowired
    private ProjectInfoMapper projectInfoMapper;
    @Autowired
    private IExtremeWeatherService iExtremeWeatherService;
    @Autowired
    private ISysDictService sysDictService;

    @Scheduled(cron = "0 0/30 * * * ?")
    public void extremeWeather() {
        log.info("极端天气 ***************");
        if (!isOpen) {
            return;
        }
        SysDict sysDict = sysDictService.getOne(new LambdaQueryWrapper<SysDict>().eq(SysDict::getDictCode, "weather_alarm"), false);
        if (StringUtils.isEmpty(sysDict)) {
            return;
        }
        LambdaQueryWrapper<ProjectInfo> projectInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        projectInfoLambdaQueryWrapper.eq(ProjectInfo::getDelFlag, Common.delete_flag.OK);
        List<ProjectInfo> projectInfos = projectInfoMapper.selectList(projectInfoLambdaQueryWrapper);
        Map<String, Object> params = new HashMap<>(16);
        if (projectInfos.size() == 0) {
            return;
        }
//        for (ProjectInfo projectInfo : projectInfos) {
        if (!StringUtils.isEmpty(sysDict.getDescription())) {
            JSONArray jsonArray = JSONArray.parseArray(sysDict.getDescription());
            for (Object point : jsonArray) {
                params.put("location", point.toString().substring(1, point.toString().length() - 1));
//                params.put("location", "115.157852,38.537458");
                String str = WeatherUtil.get(url, params, token);
                JSONObject jsonObject = JSONObject.parseObject(str);
                String code = jsonObject.getString("code");
                JSONArray data = jsonObject.getJSONArray("data");
                if (code.equals("1000") && data.size() != 0) {
                    List<ExtremeWeather> extremeWeathers = JSONArray.parseArray(JSONObject.toJSONString(data), ExtremeWeather.class);
                    for (ExtremeWeather extremeWeather : extremeWeathers) {
                        LambdaQueryWrapper<ExtremeWeather> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(ExtremeWeather::getAlertNo, extremeWeather.getAlertNo());
                        List<ExtremeWeather> list = iExtremeWeatherService.list(queryWrapper);
                        if (list.size() > 0) {
                            List<String> collect = list.stream().map(x -> x.getId()).collect(Collectors.toList());
                            LambdaUpdateWrapper<ExtremeWeather> updateWrapper = new LambdaUpdateWrapper<>();
                            updateWrapper.eq(ExtremeWeather::getAlertNo, extremeWeather.getAlertNo());
                            iExtremeWeatherService.removeByIds(collect);

                        }
                        iExtremeWeatherService.save(extremeWeather);
                    }
                }
                System.out.println(str);
            }
        }
//        }
    }

}
