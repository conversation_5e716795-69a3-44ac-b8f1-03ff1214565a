//package com.jinghe.breeze.common.thirdPort;
///
//import com.alibaba.fastjson.JSONObject;
//import com.jinghe.breeze.common.util.HttpClientUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.jeecg.common.util.RedisUtil;
//import org.jeecg.common.util.oConvertUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//@Component
//@Slf4j
//public class WeixinUtil {
//
//    @Autowired
//    private RedisUtil redisUtil;
//
//    @Value(value = "${cloud.weixin.appid}")
//    private String APPID;
//
//    @Value(value = "${cloud.weixin.secret}")
//    private String SECRET;
//
//    /**
//     * 微信小程序登录，换取openId
//     * @param code
//     * @return
//     */
//    public JSONObject getWXSessionKey(String code) throws Exception {
//        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + APPID + "&secret=" + SECRET + "&js_code=" + code + "&grant_type=authorization_code";
//        //拼接微信官方的url来获取openid
//        String s = HttpClientUtils.doGet(url);
//        JSONObject jsonObject = JSONObject.parseObject(s);
//        System.out.println(jsonObject.get("openid"));
//        return jsonObject;
//    }
//
//    /**
//     * 获取微信接口调用凭证，缓存持久化，2小时刷新，尽量保证多个环境使用同一个缓存
//     * @return
//     * @throws Exception
//     */
//    public String getWXAccessToken() throws Exception{
//        String accessTokenKey = "weixinAccessTokenKey"+APPID;
//        Object accessToken = redisUtil.get(accessTokenKey);
//        if(oConvertUtils.isNotEmpty(accessToken)){
//            return accessToken.toString();
//        }else{
//            String s = HttpClientUtils.doGet("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + APPID + "&secret=" + SECRET);
//            System.out.println("cgi-bin/token:");
//            System.out.println(s);
//            JSONObject jsonObject = JSONObject.parseObject(s);
//            if(oConvertUtils.isNotEmpty(jsonObject.get("access_token"))){
//                Object token = jsonObject.get("access_token");
//                Object expires = jsonObject.get("expires_in");
//                redisUtil.set(accessTokenKey, token, Long.parseLong(expires.toString()));
//                return token.toString();
//            }else{
//                log.error("weixin获取accessToken错误", jsonObject);
//                throw new Exception(jsonObject.get("errcode") +":"+jsonObject.get("errmsg"));
//            }
//        }
//    }
//
//    /**
//     * 获取手机号
//     * @param code
//     * @return
//     * @throws Exception
//     */
//    public JSONObject getPhoneNumber(String code) throws Exception {
//        String wxAccessToken = this.getWXAccessToken();
//        String getPhoneNumberUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + wxAccessToken;
//        JSONObject param = new JSONObject();
//        param.put("code", code);
//        String s = HttpClientUtils.doPost(getPhoneNumberUrl, "{\"code\":\"" + code + "\"}");
//        //{"errcode":0,"errmsg":"ok","phone_info":{"phoneNumber":"***********","watermark":{"appid":"wxabbde04d80d31fc5","timestamp":1707190268},"purePhoneNumber":"***********","countryCode":"86"}}
//        JSONObject post = JSONObject.parseObject(s);
//
//        return post;
//    }
//}
