# 人员轨迹回放API接口文档

## 接口信息
- **接口地址**: `GET /person/personInfo/trajectory/replay`
- **接口描述**: 获取指定人员在指定时间段内的轨迹回放数据
- **请求方式**: GET
- **是否需要认证**: 是
- **时间限制**: 最大查询跨度为一天

## 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| personId | String | 是 | 人员ID | "1234567890" |
| startTime | String | 是 | 开始时间，格式：yyyy-MM-dd HH:mm:ss | "2025-07-03 00:00:00" |
| endTime | String | 是 | 结束时间，格式：yyyy-MM-dd HH:mm:ss | "2025-07-03 23:59:59" |

## 请求示例
```
GET /person/personInfo/trajectory/replay?personId=1234567890&startTime=2025-07-03 00:00:00&endTime=2025-07-03 23:59:59
```

## 重要说明
1. **时间格式**: 必须使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **时间跨度**: 查询时间跨度不能超过一天（24小时）
3. **时间顺序**: 开始时间必须早于结束时间
4. **供应商限制**: 底层供应商API仅支持查询一天内的轨迹数据

## 返回数据格式

### 成功响应示例
```json
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": [
    {
      "puname": "1001817001",
      "gpstime": 1720003432000,
      "alarmtype": "0",
      "longitude": 120.090255,
      "latitude": 30.340866666667,
      "speed": 0.0,
      "receiveTime": "2025-07-03 10:43:53",
      "positionType": "卫星定位",
      "reportMode": "定时上报"
    },
    {
      "puname": "1001817001",
      "gpstime": 1720003442000,
      "alarmtype": "0",
      "longitude": 120.090253333333,
      "latitude": 30.340871666667,
      "speed": 0.0,
      "receiveTime": "2025-07-03 10:44:03",
      "positionType": "卫星定位",
      "reportMode": "定时上报"
    }
  ],
  "timestamp": 1720003500000
}
```

### 返回字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| puname | String | 设备编号 |
| gpstime | Long | GPS时间戳（毫秒） |
| alarmtype | String | 报警类型（"0"表示正常） |
| longitude | Double | 经度（WGS84坐标系） |
| latitude | Double | 纬度（WGS84坐标系） |
| speed | Double | 速度（km/h） |
| receiveTime | String | 接收时间（yyyy-MM-dd HH:mm:ss） |
| positionType | String | 定位类型（如：卫星定位） |
| reportMode | String | 上报模式（如：定时上报） |

### 失败响应示例
```json
{
  "success": false,
  "message": "人员ID不能为空",
  "code": 500,
  "result": null,
  "timestamp": 1641024000000
}
```

## 轨迹数据字段说明

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| puname | String | 设备编号 | "1031001" |
| gpstime | Long | GPS时间戳（秒） | 1705296000 |
| alarmtype | String | 报警类型 | "无报警"、"SOS报警"、"低电量报警" |
| longitude | Double | 经度 | 107.008272 |
| latitude | Double | 纬度 | 39.427993 |

## 业务规则

1. **时间跨度限制**: 查询的时间跨度不能超过一天
2. **日期格式**: 必须使用 `yyyy-MM-dd` 格式
3. **日期逻辑**: 开始日期不能晚于结束日期
4. **人员验证**: 必须是系统中存在的人员ID
5. **设备绑定**: 人员必须绑定了设备才能查询轨迹
6. **数据排序**: 返回的轨迹数据按时间戳升序排列

## 错误码说明

| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| "人员ID不能为空" | personId参数为空或null | 检查请求参数 |
| "查询时间跨度不能超过一天" | 时间跨度超过24小时 | 调整查询时间范围 |
| "开始日期不能晚于结束日期" | 日期逻辑错误 | 检查日期参数顺序 |
| "未找到对应的人员信息" | 人员ID不存在 | 确认人员ID正确性 |
| "该人员未绑定设备" | 人员没有绑定设备 | 先为人员绑定设备 |
| "获取人员轨迹数据失败" | 系统异常或外部API调用失败 | 联系技术支持 |

## 前端处理建议

### 1. 时间处理
```javascript
// GPS时间戳转换为本地时间
function formatGpsTime(gpstime) {
  return new Date(gpstime * 1000).toLocaleString();
}
```

### 2. 地图轨迹绘制
```javascript
// 轨迹点数据处理
function processTrajectoryData(trajectoryList) {
  return trajectoryList.map(point => ({
    lng: point.longitude,
    lat: point.latitude,
    time: formatGpsTime(point.gpstime),
    alarm: point.alarmtype,
    deviceCode: point.puname
  }));
}
```

### 3. 报警状态处理
```javascript
// 根据报警类型设置不同的标记样式
function getMarkerStyle(alarmtype) {
  switch(alarmtype) {
    case '无报警':
      return { color: 'green', icon: 'normal' };
    case 'SOS报警':
      return { color: 'red', icon: 'emergency' };
    case '低电量报警':
      return { color: 'orange', icon: 'battery' };
    default:
      return { color: 'blue', icon: 'default' };
  }
}
```

### 4. 轨迹回放控制
```javascript
// 轨迹回放控制器
class TrajectoryPlayer {
  constructor(trajectoryData) {
    this.data = trajectoryData;
    this.currentIndex = 0;
    this.isPlaying = false;
    this.speed = 1000; // 播放速度（毫秒）
  }
  
  play() {
    this.isPlaying = true;
    this.playNext();
  }
  
  pause() {
    this.isPlaying = false;
  }
  
  playNext() {
    if (!this.isPlaying || this.currentIndex >= this.data.length) {
      return;
    }
    
    // 在地图上显示当前位置
    this.showCurrentPosition(this.data[this.currentIndex]);
    this.currentIndex++;
    
    setTimeout(() => this.playNext(), this.speed);
  }
}
```

## 注意事项

1. **API Key配置**: 需要在后端配置正确的外部API Key
2. **外部API地址**: 需要根据实际的外部API接口调整请求地址和参数
3. **数据格式**: 外部API返回的数据格式可能需要调整解析逻辑
4. **网络超时**: 外部API调用可能存在网络延迟，建议设置合理的超时时间
5. **缓存策略**: 可以考虑对轨迹数据进行缓存以提高响应速度
6. **并发限制**: 注意外部API的调用频率限制

## 开发配置说明

### 后端配置项（需要开发人员配置）
```java
// 在 PersonInfoServiceImpl.java 中需要配置的常量
private static final String API_KEY = "YOUR_ACTUAL_API_KEY"; // 替换为实际的API Key
private static final String API_URL = "https://your-api-domain.com/trajectory"; // 替换为实际的API地址
```

### 架构说明
- **Controller层**: 负责接收请求参数，调用Service层异步方法，返回结果
- **Service层**: 负责业务逻辑处理，参数验证，异步调用外部API
- **异步处理**: 使用`@Async`注解和`CompletableFuture`实现异步HTTP请求
- **错误处理**: Service层抛出异常，Controller层统一处理并返回友好错误信息

### 外部API响应格式适配
当前代码假设外部API返回格式为：
```json
{
  "data": [
    {
      "puname": "设备编号",
      "gpstime": 时间戳,
      "alarmtype": "报警类型",
      "longitude": 经度,
      "latitude": 纬度
    }
  ]
}
```

如果实际API格式不同，需要修改 `PersonInfoServiceImpl.parseTrajectoryResponse` 方法中的解析逻辑。

### 异步处理配置
项目已启用异步支持：
1. **Application.java**: 添加了`@EnableAsync`注解
2. **RestTemplate**: 配置了RestTemplate Bean
3. **Service方法**: 使用`@Async`注解实现异步处理
4. **返回类型**: 使用`CompletableFuture<List<TrajectoryDTO>>`

### 性能优化建议
1. **连接池配置**: 可以为RestTemplate配置连接池以提高性能
2. **超时设置**: 建议设置合理的连接超时和读取超时时间
3. **缓存策略**: 对于相同时间段的轨迹数据可以考虑缓存
4. **限流控制**: 对外部API调用频率进行限制
