# 供应商轨迹API集成说明

## 概述

本文档说明了如何集成供应商提供的轨迹查询API，包括接口调用方式、数据格式转换和错误处理。

## 供应商API规格

### 接口信息
- **方法**: GET
- **地址**: `{API_URL}/getTrackPoints`
- **认证**: Bearer <PERSON>
- **限制**: 仅可查询设备一天内的轨迹

### 请求参数
| 参数名 | 类型 | 必须 | 说明 | 示例 |
|--------|------|------|------|------|
| PuName | String | 是 | 设备编号 | ABC001 |
| StartTime | String | 是 | 开始时间 (yyyy-MM-dd HH:mm:ss) | 2025-07-03 00:00:00 |
| EndTime | String | 是 | 结束时间 (yyyy-MM-dd HH:mm:ss) | 2025-07-03 23:59:59 |

### 响应格式
供应商API直接返回JsonArray格式：
```json
[
  {
    "longitude": "120.090255",
    "latitude": "30.340866666667",
    "speed": "0",
    "puName": "1001817001",
    "receiveTime": "2025-07-03 10:43:53",
    "positionType": "卫星定位",
    "positionTime": "2025-07-03 10:43:52",
    "reportMode": "定时上报"
  }
]
```

## 系统集成实现

### 1. API调用封装

#### 请求URL构建
```java
String requestUrl = String.format("%s/getTrackPoints?PuName=%s&StartTime=%s&EndTime=%s",
        API_URL, deviceCode, startTime, endTime);
```

#### 时间跨度验证
```java
private void validateTimeSpan(String startDate, String endDate) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date start = sdf.parse(startDate);
    Date end = sdf.parse(endDate);
    
    long diffInDays = (end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000);
    if (diffInDays > 1) {
        throw new IllegalArgumentException("轨迹查询时间跨度不能超过一天");
    }
}
```

### 2. 数据格式转换

#### 供应商数据 → 系统数据
```java
// 供应商返回的字符串格式数据
String longitudeStr = pointNode.get("longitude").asText();
String latitudeStr = pointNode.get("latitude").asText();
String speedStr = pointNode.get("speed").asText();
String positionTimeStr = pointNode.get("positionTime").asText();

// 转换为系统需要的格式
trajectory.setLongitude(Double.parseDouble(longitudeStr));
trajectory.setLatitude(Double.parseDouble(latitudeStr));
trajectory.setSpeed(Double.parseDouble(speedStr));

// 时间字符串转换为时间戳
SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
Date positionTime = sdf.parse(positionTimeStr);
trajectory.setGpstime(positionTime.getTime());
```

### 3. 错误处理策略

#### 网络异常处理
```java
try {
    ResponseEntity<String> response = restTemplate.exchange(requestUrl, HttpMethod.GET, entity, String.class);
    // 处理响应
} catch (RestClientException e) {
    log.error("调用外部轨迹API异常，设备: {}, 错误: {}", deviceCode, e.getMessage(), e);
    throw new RuntimeException("调用外部轨迹API异常: " + e.getMessage());
}
```

#### 数据解析异常处理
```java
for (JsonNode pointNode : rootNode) {
    try {
        // 解析单个轨迹点
        TrajectoryDTO trajectory = parseTrajectoryPoint(pointNode);
        trajectoryList.add(trajectory);
    } catch (Exception e) {
        log.warn("解析单个轨迹点失败，跳过该点: {}, 错误: {}", pointNode.toString(), e.getMessage());
        // 继续处理下一个点，不中断整个流程
    }
}
```

## 配置说明

### 1. 应用配置
```properties
# 供应商API配置
trajectory.api.url=https://api.supplier.com
trajectory.api.key=YOUR_API_KEY_HERE
trajectory.api.timeout=10000
```

### 2. 系统配置
```java
@Value("${trajectory.api.url}")
private String API_URL;

@Value("${trajectory.api.key}")
private String API_KEY;
```

## 性能优化

### 1. 异步处理
```java
@Async
public CompletableFuture<List<TrajectoryDTO>> getPersonTrajectoryAsync(String personId, String startTime, String endTime) {
    return getTrajectoryFromExternalApiAsync(deviceCode, startTime, endTime);
}
```

### 2. 数据排序
```java
// 按时间戳排序确保轨迹连续性
trajectoryList.sort(Comparator.comparing(TrajectoryDTO::getGpstime));
```

### 3. 批量处理
- 支持单次查询返回多个轨迹点
- 避免频繁的API调用
- 合理设置超时时间

## 监控和日志

### 1. 关键日志点
```java
log.info("调用轨迹API: {}", requestUrl);
log.info("成功解析轨迹点数量: {}", trajectoryList.size());
log.error("外部轨迹API调用失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
```

### 2. 性能监控
- API调用响应时间
- 数据解析成功率
- 异常发生频率

## 测试建议

### 1. 单元测试
```java
@Test
public void testParseTrajectoryResponse() {
    String mockResponse = "[{\"longitude\":\"120.090255\",\"latitude\":\"30.340866666667\"}]";
    List<TrajectoryDTO> result = parseTrajectoryResponse(mockResponse);
    assertEquals(1, result.size());
}
```

### 2. 集成测试
- 测试完整的API调用流程
- 验证数据格式转换正确性
- 测试异常情况处理

### 3. 压力测试
- 并发API调用测试
- 大数据量处理测试
- 网络异常恢复测试

## 注意事项

1. **时间格式**: 严格按照 `yyyy-MM-dd HH:mm:ss` 格式传递时间参数
2. **时间限制**: 供应商API限制查询跨度不超过一天
3. **数据类型**: 供应商返回的经纬度、速度等数值为字符串格式，需要转换
4. **错误恢复**: 单个轨迹点解析失败不应影响整体数据处理
5. **性能考虑**: 使用异步处理避免阻塞主线程
6. **日志记录**: 详细记录API调用和数据处理过程便于问题排查

## 扩展功能

### 1. 缓存机制
可以考虑添加Redis缓存来存储轨迹数据，减少对供应商API的调用频率。

### 2. 数据预处理
可以在数据转换过程中添加轨迹平滑、异常点过滤等预处理逻辑。

### 3. 多供应商支持
通过策略模式支持多个轨迹数据供应商的接入。
